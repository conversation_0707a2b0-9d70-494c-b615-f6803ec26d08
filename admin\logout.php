<?php
// Ensure session is started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include config to get ADMIN_URL if needed for redirect, although destroying session is main goal
require_once 'includes/config.php'; 

// Unset all session variables
$_SESSION = array();

// Destroy the session
if (session_destroy()) {
    // Redirect to login page
    header("Location: " . ADMIN_URL . "/login.php?status=loggedout");
    exit();
} else {
    // Handle potential error during session destruction (optional)
    echo "Error logging out. Please try again.";
    // Optionally redirect anyway
    // header("Location: " . ADMIN_URL . "/login.php");
    // exit();
}
?>
