<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate required fields
    if (empty($_POST['id']) || empty($_POST['username']) || empty($_POST['email']) || empty($_POST['role'])) {
        throw new Exception('Please fill in all required fields.');
    }

    // Validate email format
    if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format.');
    }

    // Validate user ID
    $user_id = (int)$_POST['id'];
    if ($user_id <= 0) {
        throw new Exception('Invalid user ID.');
    }

    // Check if user exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    if (!$user) {
        throw new Exception('User not found.');
    }

    // Check if username already exists (excluding current user)
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
    $stmt->execute([$_POST['username'], $user_id]);
    if ($stmt->fetchColumn() > 0) {
        throw new Exception('Username already exists.');
    }

    // Check if email already exists (excluding current user)
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = ? AND id != ?");
    $stmt->execute([$_POST['email'], $user_id]);
    if ($stmt->fetchColumn() > 0) {
        throw new Exception('Email already exists.');
    }

    // Handle avatar upload
    $avatar_filename = $user['avatar']; // Keep existing avatar by default
    if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['avatar'];
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 5 * 1024 * 1024; // 5MB

        // Validate file type
        if (!in_array($file['type'], $allowed_types)) {
            throw new Exception('Invalid file type. Only JPG, PNG, and GIF files are allowed.');
        }

        // Validate file size
        if ($file['size'] > $max_size) {
            throw new Exception('File size exceeds 5MB limit.');
        }

        // Create upload directory if it doesn't exist
        $upload_dir = '../../uploads/avatars/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $new_avatar_filename = uniqid() . '.' . $extension;
        $target_file = $upload_dir . $new_avatar_filename;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $target_file)) {
            // Delete old avatar if exists
            if (!empty($user['avatar'])) {
                $old_avatar_path = '../../uploads/avatars/' . $user['avatar'];
                if (file_exists($old_avatar_path)) {
                    unlink($old_avatar_path);
                }
            }
            $avatar_filename = $new_avatar_filename;
        } else {
            throw new Exception('Failed to upload new avatar.');
        }
    }

    // Begin transaction
    $conn->beginTransaction();

    // Prepare user data
    $user_data = [
        'id' => $user_id,
        'username' => trim($_POST['username']),
        'email' => trim($_POST['email']),
        'role' => $_POST['role'],
        'first_name' => trim($_POST['first_name'] ?? ''),
        'last_name' => trim($_POST['last_name'] ?? ''),
        'avatar' => $avatar_filename
    ];

    // Add password to update if provided
    if (!empty($_POST['password'])) {
        if (strlen($_POST['password']) < 8) {
            throw new Exception('Password must be at least 8 characters long.');
        }
        $user_data['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
    }

    // Build SQL query
    $sql = "UPDATE users SET 
                username = :username,
                email = :email,
                role = :role,
                first_name = :first_name,
                last_name = :last_name,
                avatar = :avatar";

    // Add password to query if provided
    if (!empty($user_data['password'])) {
        $sql .= ", password = :password";
    }

    $sql .= " WHERE id = :id";

    $stmt = $conn->prepare($sql);
    
    if ($stmt->execute($user_data)) {
        $conn->commit();
        $response['success'] = true;
        $response['message'] = 'User updated successfully.';
    } else {
        throw new Exception('Failed to update user.');
    }

} catch (Exception $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // Delete newly uploaded avatar if exists
    if (!empty($new_avatar_filename)) {
        $avatar_path = '../../uploads/avatars/' . $new_avatar_filename;
        if (file_exists($avatar_path)) {
            unlink($avatar_path);
        }
    }
    
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 