<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/store_functions.php';

// Check if user is logged in
requireLogin();

// Get store ID from URL
$store_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get store data
$store = getStoreById($store_id);

// If store not found, redirect to stores page
if (!$store) {
    $_SESSION['error'] = 'Store not found.';
    header('Location: stores.php');
    exit;
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit Store</h1>
        <a href="stores.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Stores
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Main Store Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="editStoreForm" enctype="multipart/form-data">
                        <input type="hidden" name="id" value="<?php echo $store['id']; ?>">
                        <input type="hidden" name="current_logo" value="<?php echo htmlspecialchars($store['logo']); ?>">

                        <div class="mb-4">
                            <label for="name" class="form-label">Store Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-lg" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($store['name']); ?>" required>
                            <div class="form-text">Enter the name of the online store.</div>
                        </div>

                        <div class="mb-4">
                            <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="slug" name="slug" 
                                   value="<?php echo htmlspecialchars($store['slug']); ?>" required>
                            <div class="form-text">URL-friendly version of the store name (e.g., amazon, walmart).</div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($store['description']); ?></textarea>
                            <div class="form-text">Provide a brief description of the store and its offerings.</div>
                        </div>

                        <div class="mb-4">
                            <label for="website_url" class="form-label">Website URL <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="website_url" name="website_url" 
                                   value="<?php echo htmlspecialchars($store['website_url']); ?>" 
                                   placeholder="https://example.com" required>
                            <div class="form-text">The main website URL of the store.</div>
                        </div>

                        <div class="mb-4">
                            <label for="affiliate_id" class="form-label">Affiliate ID</label>
                            <input type="text" class="form-control" id="affiliate_id" name="affiliate_id" 
                                   value="<?php echo htmlspecialchars($store['affiliate_id']); ?>">
                            <div class="form-text">Your affiliate ID for this store (if applicable).</div>
                        </div>

                        <div class="mb-4">
                            <label for="logo" class="form-label">Store Logo</label>
                            <?php if (!empty($store['logo'])): ?>
                            <div class="mb-2">
                                <img src="<?php echo SITE_URL; ?>/uploads/stores/<?php echo htmlspecialchars($store['logo']); ?>" 
                                     class="img-thumbnail" style="max-width: 200px;">
                            </div>
                            <?php endif; ?>
                            <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                            <div class="form-text">Recommended size: 200x200 pixels. Maximum file size: 2MB.</div>
                            <div id="logoPreview" class="mt-2"></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Publish</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" form="editStoreForm">
                            <option value="active" <?php echo $store['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $store['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" form="editStoreForm">
                            <i class="fas fa-save"></i> Update Store
                        </button>
                        <a href="stores.php" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>

            <!-- Store Info -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Store Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Created</label>
                        <div><?php echo date('M j, Y', strtotime($store['created_at'])); ?></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Total Coupons</label>
                        <div><?php echo $store['coupon_count']; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from name
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    
    nameInput.addEventListener('input', function() {
        if (!slugInput.value) {
            slugInput.value = this.value
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '');
        }
    });

    // Image preview
    document.getElementById('logo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file size (2MB max)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                this.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    let warning = '';
                    if (this.width < 200 || this.height < 200) {
                        warning = '<div class="alert alert-warning mt-2">Image is smaller than recommended size (200x200)</div>';
                    }
                    document.getElementById('logoPreview').innerHTML = `
                        <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">
                        <div class="text-muted mt-1">Size: ${this.width}x${this.height}</div>
                        ${warning}
                    `;
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // Form submission
    document.getElementById('editStoreForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);

        // Submit the form
        fetch('ajax/update_store.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = 'stores.php';
            } else {
                alert(data.message || 'Error updating store');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the store');
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?> 