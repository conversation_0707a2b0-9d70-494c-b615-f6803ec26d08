<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/store_functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate input
    if (empty($_POST['id'])) {
        throw new Exception('Store ID is required.');
    }

    $store_id = (int)$_POST['id'];
    
    // Delete the store
    if (deleteStore($store_id)) {
        $response['success'] = true;
        $response['message'] = 'Store deleted successfully.';
    } else {
        throw new Exception('Failed to delete store.');
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 