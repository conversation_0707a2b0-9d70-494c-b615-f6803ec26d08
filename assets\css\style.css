/* Main Styles for Trip Nest */

/* Global Styles */
:root {
    --primary-color: #0A0A4A;
    --secondary-color: #FF7F00;
    --light-bg: #f5f5f5;
    --text-color: #333;
    --light-text: #777;
    --border-color: #ddd;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
    background-image: url('../images/orange-swirl-bg.png');
    background-repeat: repeat-y;
    background-position: right;
    background-size: 150px;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.main-content {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
}

.content-area {
    flex: 1;
    min-width: 0;
    margin-right: 30px;
}

/* Hero Section */
.hero-section {
    position: relative;
    margin-bottom: 30px;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
}

.hero-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
}

.hero-slideshow {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.slide {
    display: none;
    position: relative;
}

.slide.active {
    display: block;
}

.hero-content {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #ffc107;
    padding: 25px;
    width: 40%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: hidden;
}

.hero-content-top {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.hero-content h2 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #000;
    line-height: 1.3;
    font-weight: bold;
}

.hero-content p {
    margin-bottom: 15px;
    line-height: 1.5;
    color: #000;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hero-meta {
    font-size: 0.85rem;
    margin-bottom: 10px;
    color: #000;
}

.hero-meta a {
    color: #0A0A4A;
    text-decoration: none;
}

.hero-meta a:hover {
    text-decoration: underline;
}

.hero-content-bottom {
    display: flex;
    justify-content: flex-end;
}

.btn-primary {
    display: inline-block;
    background-color: #0A0A4A;
    color: white;
    padding: 10px 18px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
    text-align: center;
    width: fit-content;
}

.btn-primary:hover {
    background-color: #FF7F00;
}

/* Featured Post Label */
.featured-label {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: rgba(255, 127, 0, 0.9);
    color: white;
    padding: 5px 10px;
    font-size: 0.8rem;
    font-weight: bold;
    border-radius: 3px;
    z-index: 5;
}

/* Thumbnail Navigation */
.thumbnail-nav {
    display: flex;
    position: relative;
    width: 100%;
    height: 50px;
    overflow: hidden;
}

.thumbnail-nav img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.7);
}

.thumbnail-item {
    flex: 1;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.thumbnail-item.active img {
    filter: brightness(1);
}

.thumbnail-item.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-top: 3px solid #FF7F00;
}

.next-slide-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 100%;
    background-color: #FF7F00;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
}

.next-slide-btn:hover {
    background-color: #0A0A4A;
}

/* Travel Articles */
.travel-articles {
    margin-bottom: 40px;
}

.article-heading {
    font-size: 1.3rem;
    padding-bottom: 10px;
    margin-bottom: 25px;
    border-bottom: 2px solid var(--secondary-color);
    color: var(--primary-color);
    font-weight: bold;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    margin-bottom: 20px;
}

.article {
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.article:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.article-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.article-content {
    padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.article-content h3 {
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.article-content h3 a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s;
}

.article-content h3 a:hover {
    color: var(--secondary-color);
}

.article-content p {
    font-size: 0.9rem;
    color: var(--light-text);
    margin-bottom: 15px;
    flex: 1;
}

.read-more {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: bold;
    align-self: flex-start;
    margin-top: auto;
}

.read-more:hover {
    text-decoration: underline;
}

.section-footer {
    text-align: center;
    margin-top: 20px;
}

.view-all-btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
}

.view-all-btn:hover {
    background-color: var(--secondary-color);
}

/* Coupon Section */
.coupon-section {
    background-color: #f9f9f9;
    padding: 20px;
    margin-bottom: 30px;
    text-align: center;
    border-radius: 5px;
    border: 1px dashed var(--secondary-color);
}

.coupon-section h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.coupon-btn {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 8px 20px;
    border-radius: 3px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
}

.coupon-btn:hover {
    background-color: var(--primary-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 5px 10px;
    margin: 0 3px;
    border: 1px solid var(--border-color);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 3px;
}

.pagination a:hover {
    background-color: var(--primary-color);
    color: white;
}

.pagination .current {
    background-color: var(--primary-color);
    color: white;
}

/* Sidebar */
.sidebar {
    width: 300px;
}

.sidebar-widget {
    margin-bottom: 30px;
    background-color: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.sidebar-widget h3 {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 15px;
    margin: 0;
    font-size: 1rem;
}

.widget-content {
    padding: 15px;
}

.ad-widget {
    text-align: center;
}

.ad-widget img {
    max-width: 100%;
    border-radius: 3px;
}

.popular-posts ul {
    list-style: none;
    padding: 0;
}

.popular-posts li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.popular-posts li:last-child {
    border-bottom: none;
}

.popular-posts a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    display: block;
    transition: color 0.3s;
}

.popular-posts a:hover {
    color: var(--secondary-color);
}

.popular-posts .post-date {
    font-size: 0.8rem;
    color: var(--light-text);
    display: block;
    margin-top: 3px;
}

/* Footer Icons */
.footer-icons {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.footer-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 15px;
    text-decoration: none;
    color: var(--primary-color);
}

.icon-circle {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.icon-circle i {
    color: white;
    font-size: 1.5rem;
}

.footer-icon span {
    font-size: 0.8rem;
    text-align: center;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .hero-content h2 {
        font-size: 1.4rem;
    }
    
    .hero-content p {
        font-size: 0.85rem;
        -webkit-line-clamp: 5;
    }
}

@media (max-width: 992px) {
    .main-content {
        flex-direction: column;
    }
    
    .content-area {
        margin-right: 0;
        margin-bottom: 30px;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .hero-content {
        width: 50%;
        padding: 20px;
    }
    
    .hero-content h2 {
        font-size: 1.3rem;
    }
    
    .hero-content p {
        font-size: 0.8rem;
        line-height: 1.4;
        -webkit-line-clamp: 4;
    }
    
    .btn-primary {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
    
    .articles-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .hero-content {
        width: 100%;
        height: auto;
        position: relative;
        background-color: #ffc107;
    }
    
    .hero-image {
        height: 250px;
    }
    
    .hero-content p {
        -webkit-line-clamp: 3;
    }
    
    .thumbnail-nav {
        height: 40px;
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .article-image {
        height: 200px;
    }
    
    .footer-icons {
        flex-wrap: wrap;
    }
    
    .footer-icon {
        margin: 10px;
    }
}

@media (max-width: 576px) {
    .hero-image {
        height: 200px;
    }
    
    .thumbnail-nav {
        height: 30px;
    }
    
    .hero-content h2 {
        font-size: 1.2rem;
    }
    
    .hero-content p {
        font-size: 0.85rem;
        -webkit-line-clamp: 2;
    }
    
    .btn-primary {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
    
    .article-heading {
        font-size: 1.2rem;
    }
    
    .article-image {
        height: 180px;
    }
} 