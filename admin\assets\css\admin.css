/* Admin Panel Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f1c40f;
    --text-color: #2c3e50;
    --light-gray: #ecf0f1;
    --dark-gray: #7f8c8d;
    --sidebar-width: 250px;
    --header-height: 60px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    color: var(--text-color);
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

/* Wrapper */
.wrapper {
    display: flex;
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: #f5f6fa;
}

/* Sidebar */
#sidebar {
    width: var(--sidebar-width);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    background: var(--primary-color);
    color: #fff;
    transition: all 0.3s;
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

#sidebar.active {
    margin-left: calc(-1 * var(--sidebar-width));
}

#sidebar .sidebar-header {
    padding: 15px 20px;
    background: var(--secondary-color);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#sidebar .sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: #fff;
}

#sidebar ul.components {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#sidebar ul li a {
    padding: 12px 20px;
    font-size: 1rem;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

#sidebar ul li a:hover {
    background: var(--secondary-color);
    color: #fff;
    border-left-color: var(--accent-color);
}

#sidebar ul li.active > a {
    background: var(--accent-color);
    color: #fff;
    border-left-color: #fff;
}

#sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Content */
.content {
    width: calc(100% - var(--sidebar-width));
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    transition: all 0.3s;
    padding: 0;
    background: #f5f6fa;
}

.content.active {
    width: 100%;
    margin-left: 0;
}

/* Content Header */
.content-header {
    padding: 15px 0;
    background: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.content-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Main Content Container */
.container-fluid {
    padding: 0 20px;
    width: 100%;
    max-width: 100%;
}

/* Navbar */
.navbar {
    padding: 10px 20px;
    background: #fff;
    border: none;
    border-radius: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 0;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    background: #fff;
}

.card-body {
    padding: 1.25rem;
}

.card-body.p-0 {
    padding: 0;
}

/* Tables */
.table {
    margin: 0;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    border-top: none;
    padding: 12px 15px;
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

/* Forms */
.form-control {
    border-radius: 5px;
    border: 1px solid #ddd;
    padding: 10px 15px;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Buttons */
.btn {
    border-radius: 5px;
    padding: 8px 15px;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 5px;
    margin-bottom: 20px;
}

/* Dashboard Stats */
.stats-card {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.stats-card .icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stats-card .number {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.stats-card .label {
    color: var(--dark-gray);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    :root {
        --sidebar-width: 220px;
    }
    
    .content-header h2 {
        font-size: 1.3rem;
    }
}

@media (max-width: 767.98px) {
    #sidebar {
        margin-left: calc(-1 * var(--sidebar-width));
    }
    
    #sidebar.active {
        margin-left: 0;
    }
    
    .content {
        width: 100%;
        margin-left: 0;
    }
    
    .content.active {
        margin-left: var(--sidebar-width);
    }
    
    .container-fluid {
        padding: 0 15px;
    }
    
    .table-responsive {
        margin: 0;
    }
}

@media (max-width: 575.98px) {
    .content-header .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table th,
    .table td {
        padding: 8px 12px;
    }
}

/* TinyMCE Editor */
.tox-tinymce {
    border-radius: 5px !important;
}

/* Image Upload Preview */
.image-preview {
    max-width: 200px;
    max-height: 200px;
    margin-top: 10px;
    border-radius: 5px;
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--dark-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
