<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/post_functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized access']));
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (empty($input['id'])) {
    die(json_encode(['success' => false, 'message' => 'Post ID is required']));
}

// Delete the post
if (deletePost($input['id'])) {
    echo json_encode([
        'success' => true,
        'message' => 'Post deleted successfully'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to delete post'
    ]);
} 