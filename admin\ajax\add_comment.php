<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate required fields
    if (empty($_POST['content']) || empty($_POST['post_id'])) {
        throw new Exception('Missing required fields.');
    }

    // Validate post ID
    $post_id = (int)$_POST['post_id'];
    if ($post_id <= 0) {
        throw new Exception('Invalid post ID.');
    }

    // Check if post exists
    $stmt = $conn->prepare("SELECT * FROM posts WHERE id = ?");
    $stmt->execute([$post_id]);
    if (!$stmt->fetch()) {
        throw new Exception('Post not found.');
    }

    // Validate parent comment if provided
    $parent_id = null;
    if (!empty($_POST['parent_id'])) {
        $parent_id = (int)$_POST['parent_id'];
        if ($parent_id <= 0) {
            throw new Exception('Invalid parent comment ID.');
        }

        // Check if parent comment exists and belongs to the same post
        $stmt = $conn->prepare("SELECT * FROM comments WHERE id = ? AND post_id = ?");
        $stmt->execute([$parent_id, $post_id]);
        if (!$stmt->fetch()) {
            throw new Exception('Parent comment not found or does not belong to this post.');
        }
    }

    // Begin transaction
    $conn->beginTransaction();

    // Prepare comment data
    $comment_data = [
        'post_id' => $post_id,
        'user_id' => $_SESSION['admin_id'],
        'parent_id' => $parent_id,
        'content' => trim($_POST['content']),
        'status' => 'approved' // Admin comments are automatically approved
    ];

    // Insert comment
    $sql = "INSERT INTO comments (
                post_id, user_id, parent_id, content, status
            ) VALUES (
                :post_id, :user_id, :parent_id, :content, :status
            )";

    $stmt = $conn->prepare($sql);
    
    if ($stmt->execute($comment_data)) {
        $conn->commit();
        $response['success'] = true;
        $response['message'] = 'Comment added successfully.';
    } else {
        throw new Exception('Failed to add comment.');
    }

} catch (Exception $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 