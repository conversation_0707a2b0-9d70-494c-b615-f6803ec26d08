<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
requireLogin();

// Define the upload directory
$upload_dir = '../uploads/content/';

// Create the directory if it doesn't exist
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// Function to handle file upload
function handleUpload() {
    global $upload_dir;
    
    // Response array
    $response = array();
    
    // Check if file was uploaded
    if (!isset($_FILES['upload']) || $_FILES['upload']['error'] > 0) {
        $response['uploaded'] = 0;
        $response['error'] = array(
            'message' => 'Error uploading file: ' . ($_FILES['upload']['error'] ?? 'No file uploaded')
        );
        return $response;
    }
    
    // Get file info
    $file = $_FILES['upload'];
    $file_name = $file['name'];
    $file_tmp = $file['tmp_name'];
    $file_size = $file['size'];
    $file_error = $file['error'];
    
    // Get file extension
    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
    
    // Allowed file extensions
    $allowed_ext = array('jpg', 'jpeg', 'png', 'gif', 'webp');
    
    // Check if file extension is allowed
    if (!in_array($file_ext, $allowed_ext)) {
        $response['uploaded'] = 0;
        $response['error'] = array(
            'message' => 'Invalid file extension. Allowed extensions: ' . implode(', ', $allowed_ext)
        );
        return $response;
    }
    
    // Check file size (max 5MB)
    if ($file_size > 5242880) {
        $response['uploaded'] = 0;
        $response['error'] = array(
            'message' => 'File size too large. Maximum size: 5MB'
        );
        return $response;
    }
    
    // Generate a unique file name
    $new_file_name = uniqid() . '.' . $file_ext;
    $file_destination = $upload_dir . $new_file_name;
    
    // Move the file to the upload directory
    if (move_uploaded_file($file_tmp, $file_destination)) {
        $response['uploaded'] = 1;
        $response['fileName'] = $new_file_name;
        $response['url'] = '/uploads/content/' . $new_file_name;
        return $response;
    } else {
        $response['uploaded'] = 0;
        $response['error'] = array(
            'message' => 'Error moving uploaded file'
        );
        return $response;
    }
}

// Handle the upload
$response = handleUpload();

// Return the response as JSON
header('Content-Type: application/json');
echo json_encode($response);
exit;
