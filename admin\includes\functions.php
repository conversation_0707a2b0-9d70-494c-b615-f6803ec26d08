<?php
/**
 * Additional helper functions for the admin panel
 * Note: Basic functions are already defined in config.php
 */

if (!function_exists('formatDate')) {
    /**
     * Format date
     */
    function formatDate($date, $format = 'Y-m-d H:i:s') {
        return date($format, strtotime($date));
    }
}

if (!function_exists('deleteFile')) {
    /**
     * Delete file
     */
    function deleteFile($filepath) {
        if (file_exists($filepath)) {
            return unlink($filepath);
        }
        return false;
    }
}

if (!function_exists('getPagination')) {
    /**
     * Get pagination
     */
    function getPagination($total_items, $items_per_page = 10, $current_page = 1) {
        $total_pages = ceil($total_items / $items_per_page);
        $current_page = max(1, min($current_page, $total_pages));
        
        $offset = ($current_page - 1) * $items_per_page;
        
        return [
            'current_page' => $current_page,
            'items_per_page' => $items_per_page,
            'total_pages' => $total_pages,
            'offset' => $offset
        ];
    }
}

if (!function_exists('paginationLinks')) {
    /**
     * Generate pagination links
     */
    function paginationLinks($total_pages, $current_page, $url_pattern) {
        $links = [];
        
        // Previous link
        if ($current_page > 1) {
            $links[] = [
                'url' => sprintf($url_pattern, $current_page - 1),
                'text' => '&laquo; Previous',
                'active' => false
            ];
        }
        
        // Page links
        for ($i = 1; $i <= $total_pages; $i++) {
            if ($i == 1 || $i == $total_pages || ($i >= $current_page - 2 && $i <= $current_page + 2)) {
                $links[] = [
                    'url' => sprintf($url_pattern, $i),
                    'text' => $i,
                    'active' => ($i == $current_page)
                ];
            } elseif ($i == $current_page - 3 || $i == $current_page + 3) {
                $links[] = [
                    'url' => '#',
                    'text' => '...',
                    'active' => false
                ];
            }
        }
        
        // Next link
        if ($current_page < $total_pages) {
            $links[] = [
                'url' => sprintf($url_pattern, $current_page + 1),
                'text' => 'Next &raquo;',
                'active' => false
            ];
        }
        
        return $links;
    }
}

if (!function_exists('isJson')) {
    /**
     * Check if string is valid JSON
     */
    function isJson($string) {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}

if (!function_exists('getIpAddress')) {
    /**
     * Get user IP address
     */
    function getIpAddress() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
}

if (!function_exists('generateRandomString')) {
    /**
     * Generate random string
     */
    function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $string = '';
        
        for ($i = 0; $i < $length; $i++) {
            $string .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $string;
    }
}

if (!function_exists('formatFileSize')) {
    /**
     * Format file size
     */
    function formatFileSize($bytes) {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } elseif ($bytes > 1) {
            return $bytes . ' bytes';
        } elseif ($bytes == 1) {
            return '1 byte';
        } else {
            return '0 bytes';
        }
    }
}

if (!function_exists('cleanUrl')) {
    /**
     * Clean URL
     */
    function cleanUrl($url) {
        return filter_var($url, FILTER_SANITIZE_URL);
    }
}

if (!function_exists('isValidEmail')) {
    /**
     * Validate email
     */
    function isValidEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }
}

if (!function_exists('formatCurrency')) {
    /**
     * Format currency
     */
    function formatCurrency($amount, $currency = 'USD') {
        $formats = [
            'USD' => ['symbol' => '$', 'decimals' => 2, 'dec_point' => '.', 'thousands_sep' => ','],
            'EUR' => ['symbol' => '€', 'decimals' => 2, 'dec_point' => ',', 'thousands_sep' => '.'],
            'GBP' => ['symbol' => '£', 'decimals' => 2, 'dec_point' => '.', 'thousands_sep' => ',']
        ];
        
        $format = $formats[$currency] ?? $formats['USD'];
        
        return $format['symbol'] . number_format(
            $amount,
            $format['decimals'],
            $format['dec_point'],
            $format['thousands_sep']
        );
    }
} 