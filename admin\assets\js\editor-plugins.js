/**
 * TripNest Custom CKEditor 4 Plugins
 * 
 * This file contains custom plugins for the CKEditor used in TripNest travel blog.
 */

// Location Map Plugin
CKEDITOR.plugins.add('LocationMap', {
    icons: 'locationmap',
    init: function(editor) {
        editor.addCommand('insertLocationMap', {
            exec: function(editor) {
                // Create dialog for map selection
                var dialogDefinition = {
                    title: 'Insert Location Map',
                    minWidth: 400,
                    minHeight: 300,
                    contents: [
                        {
                            id: 'tab1',
                            label: 'Basic Settings',
                            elements: [
                                {
                                    type: 'text',
                                    id: 'location',
                                    label: 'Location Name',
                                    validate: CKEDITOR.dialog.validate.notEmpty('Location name cannot be empty.')
                                },
                                {
                                    type: 'text',
                                    id: 'latitude',
                                    label: 'Latitude (optional)',
                                    validate: function(value) {
                                        if (value && (isNaN(parseFloat(value)) || parseFloat(value) < -90 || parseFloat(value) > 90)) {
                                            return 'Latitude must be a number between -90 and 90.';
                                        }
                                        return true;
                                    }
                                },
                                {
                                    type: 'text',
                                    id: 'longitude',
                                    label: 'Longitude (optional)',
                                    validate: function(value) {
                                        if (value && (isNaN(parseFloat(value)) || parseFloat(value) < -180 || parseFloat(value) > 180)) {
                                            return 'Longitude must be a number between -180 and 180.';
                                        }
                                        return true;
                                    }
                                },
                                {
                                    type: 'select',
                                    id: 'mapType',
                                    label: 'Map Type',
                                    items: [
                                        ['Standard', 'standard'],
                                        ['Satellite', 'satellite'],
                                        ['Terrain', 'terrain']
                                    ],
                                    'default': 'standard'
                                },
                                {
                                    type: 'select',
                                    id: 'mapSize',
                                    label: 'Map Size',
                                    items: [
                                        ['Small', 'small'],
                                        ['Medium', 'medium'],
                                        ['Large', 'large'],
                                        ['Custom', 'custom']
                                    ],
                                    'default': 'medium',
                                    onChange: function(api) {
                                        // Show/hide custom size fields
                                        var dialog = this.getDialog();
                                        var showCustom = (this.getValue() === 'custom');
                                        dialog.getContentElement('tab1', 'customWidth').getElement().getParent().getParent()[showCustom ? 'show' : 'hide']();
                                        dialog.getContentElement('tab1', 'customHeight').getElement().getParent().getParent()[showCustom ? 'show' : 'hide']();
                                    }
                                },
                                {
                                    type: 'text',
                                    id: 'customWidth',
                                    label: 'Custom Width (px)',
                                    'default': '600',
                                    validate: CKEDITOR.dialog.validate.regex(/^\d+$/, 'Custom width must be a positive number.')
                                },
                                {
                                    type: 'text',
                                    id: 'customHeight',
                                    label: 'Custom Height (px)',
                                    'default': '400',
                                    validate: CKEDITOR.dialog.validate.regex(/^\d+$/, 'Custom height must be a positive number.')
                                },
                                {
                                    type: 'checkbox',
                                    id: 'addDescription',
                                    label: 'Add Description',
                                    'default': true
                                }
                            ]
                        }
                    ],
                    onShow: function() {
                        var dialog = this;
                        var showCustom = (dialog.getValueOf('tab1', 'mapSize') === 'custom');
                        dialog.getContentElement('tab1', 'customWidth').getElement().getParent().getParent()[showCustom ? 'show' : 'hide']();
                        dialog.getContentElement('tab1', 'customHeight').getElement().getParent().getParent()[showCustom ? 'show' : 'hide']();
                    },
                    onOk: function() {
                        var dialog = this;
                        var location = dialog.getValueOf('tab1', 'location');
                        var latitude = dialog.getValueOf('tab1', 'latitude');
                        var longitude = dialog.getValueOf('tab1', 'longitude');
                        var mapType = dialog.getValueOf('tab1', 'mapType');
                        var mapSize = dialog.getValueOf('tab1', 'mapSize');
                        var customWidth = dialog.getValueOf('tab1', 'customWidth');
                        var customHeight = dialog.getValueOf('tab1', 'customHeight');
                        var addDescription = dialog.getValueOf('tab1', 'addDescription');
                        
                        // Determine map dimensions
                        var width, height;
                        switch(mapSize) {
                            case 'small':
                                width = 400;
                                height = 300;
                                break;
                            case 'medium':
                                width = 600;
                                height = 400;
                                break;
                            case 'large':
                                width = 800;
                                height = 500;
                                break;
                            case 'custom':
                                width = parseInt(customWidth, 10);
                                height = parseInt(customHeight, 10);
                                break;
                        }
                        
                        // Create map embed code
                        var escapedLocation = encodeURIComponent(location);
                        var mapUrl = '';
                        
                        if (latitude && longitude) {
                            mapUrl = `https://maps.google.com/maps?q=${latitude},${longitude}&t=${mapType === 'satellite' ? 'k' : (mapType === 'terrain' ? 'p' : 'm')}&z=13&ie=UTF8&iwloc=&output=embed`;
                        } else {
                            mapUrl = `https://maps.google.com/maps?q=${escapedLocation}&t=${mapType === 'satellite' ? 'k' : (mapType === 'terrain' ? 'p' : 'm')}&z=13&ie=UTF8&iwloc=&output=embed`;
                        }
                        
                        // Generate HTML
                        var html = `<div class="travel-map-container">
                            <iframe width="${width}" height="${height}" id="gmap_canvas" src="${mapUrl}" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"></iframe>`;
                            
                        if (addDescription) {
                            html += `<div class="map-description"><p>Map showing ${location}. Explore this area during your visit!</p></div>`;
                        }
                        
                        html += `</div>`;
                        
                        // Insert the map
                        editor.insertHtml(html);
                    }
                };
                
                editor.openDialog('locationMapDialog', function(dialog) {
                    // Dialog opened callback if needed
                    return dialogDefinition;
                });
            }
        });
        
        editor.ui.addButton('LocationMap', {
            label: 'Insert Location Map',
            command: 'insertLocationMap',
            toolbar: 'travel'
        });
    }
});

// Weather Widget Plugin
CKEDITOR.plugins.add('WeatherWidget', {
    icons: 'weatherwidget',
    init: function(editor) {
        editor.addCommand('insertWeatherWidget', {
            exec: function(editor) {
                // Create dialog for weather widget
                var dialogDefinition = {
                    title: 'Insert Weather Widget',
                    minWidth: 400,
                    minHeight: 200,
                    contents: [
                        {
                            id: 'tab1',
                            label: 'Basic Settings',
                            elements: [
                                {
                                    type: 'text',
                                    id: 'location',
                                    label: 'Location',
                                    validate: CKEDITOR.dialog.validate.notEmpty('Location cannot be empty.')
                                },
                                {
                                    type: 'select',
                                    id: 'widgetType',
                                    label: 'Widget Type',
                                    items: [
                                        ['Current Weather', 'current'],
                                        ['Weather Forecast', 'forecast']
                                    ],
                                    'default': 'forecast'
                                },
                                {
                                    type: 'select',
                                    id: 'forecastDays',
                                    label: 'Forecast Days',
                                    items: [
                                        ['3 Days', '3'],
                                        ['5 Days', '5'],
                                        ['7 Days', '7']
                                    ],
                                    'default': '5'
                                },
                                {
                                    type: 'select',
                                    id: 'unit',
                                    label: 'Temperature Unit',
                                    items: [
                                        ['Celsius', 'c'],
                                        ['Fahrenheit', 'f']
                                    ],
                                    'default': 'c'
                                },
                                {
                                    type: 'select',
                                    id: 'theme',
                                    label: 'Widget Theme',
                                    items: [
                                        ['Light', 'light'],
                                        ['Dark', 'dark'],
                                        ['Blue', 'blue']
                                    ],
                                    'default': 'light'
                                }
                            ]
                        }
                    ],
                    onOk: function() {
                        var dialog = this;
                        var location = dialog.getValueOf('tab1', 'location');
                        var widgetType = dialog.getValueOf('tab1', 'widgetType');
                        var forecastDays = dialog.getValueOf('tab1', 'forecastDays');
                        var unit = dialog.getValueOf('tab1', 'unit');
                        var theme = dialog.getValueOf('tab1', 'theme');
                        
                        // Generate HTML for the weather widget
                        // Note: This is a placeholder. In a real implementation, you would integrate with a weather API
                        var html = `<div class="weather-widget weather-theme-${theme}" data-location="${location}" data-type="${widgetType}" data-days="${forecastDays}" data-unit="${unit}">
                            <div class="weather-header">
                                <h4>Weather for ${location}</h4>
                            </div>
                            <div class="weather-content">
                                <div class="weather-loading">Loading weather data...</div>
                                <div class="weather-data" style="display: none;">
                                    <div class="current-weather">
                                        <div class="weather-icon">☀️</div>
                                        <div class="weather-temp">25°${unit.toUpperCase()}</div>
                                        <div class="weather-desc">Sunny</div>
                                    </div>
                                    ${widgetType === 'forecast' ? `
                                    <div class="weather-forecast">
                                        <div class="forecast-title">Forecast</div>
                                        <div class="forecast-items">
                                            <div class="forecast-day">
                                                <div class="day-name">Mon</div>
                                                <div class="day-icon">☀️</div>
                                                <div class="day-temp">25°${unit.toUpperCase()}</div>
                                            </div>
                                            <div class="forecast-day">
                                                <div class="day-name">Tue</div>
                                                <div class="day-icon">🌤️</div>
                                                <div class="day-temp">23°${unit.toUpperCase()}</div>
                                            </div>
                                            <div class="forecast-day">
                                                <div class="day-name">Wed</div>
                                                <div class="day-icon">🌧️</div>
                                                <div class="day-temp">20°${unit.toUpperCase()}</div>
                                            </div>
                                        </div>
                                    </div>` : ''}
                                </div>
                            </div>
                            <div class="weather-footer">
                                <div class="weather-note">This is a sample weather widget. In a production environment, it would connect to a real weather API.</div>
                            </div>
                        </div>
                        <script>
                            // Simulate weather data loading
                            setTimeout(function() {
                                var widget = document.querySelector('.weather-widget[data-location="${location}"]');
                                if (widget) {
                                    widget.querySelector('.weather-loading').style.display = 'none';
                                    widget.querySelector('.weather-data').style.display = 'block';
                                }
                            }, 1500);
                        </script>`;
                        
                        // Insert the weather widget
                        editor.insertHtml(html);
                    }
                };
                
                editor.openDialog('weatherWidgetDialog', function(dialog) {
                    // Dialog opened callback if needed
                    return dialogDefinition;
                });
            }
        });
        
        editor.ui.addButton('WeatherWidget', {
            label: 'Insert Weather Widget',
            command: 'insertWeatherWidget',
            toolbar: 'travel'
        });
    }
});

// Attractions Gallery Plugin
CKEDITOR.plugins.add('AttractionsGallery', {
    icons: 'attractionsgallery',
    init: function(editor) {
        editor.addCommand('insertAttractionsGallery', {
            exec: function(editor) {
                var dialogDefinition = {
                    title: 'Insert Attractions Gallery',
                    minWidth: 500,
                    minHeight: 400,
                    contents: [
                        {
                            id: 'tab1',
                            label: 'Gallery Settings',
                            elements: [
                                {
                                    type: 'text',
                                    id: 'galleryTitle',
                                    label: 'Gallery Title',
                                    'default': 'Top Attractions'
                                },
                                {
                                    type: 'select',
                                    id: 'galleryLayout',
                                    label: 'Gallery Layout',
                                    items: [
                                        ['Grid (3 columns)', 'grid-3'],
                                        ['Grid (2 columns)', 'grid-2'],
                                        ['Slider', 'slider'],
                                        ['List', 'list']
                                    ],
                                    'default': 'grid-3'
                                },
                                {
                                    type: 'html',
                                    html: '<div id="attractionItems" style="border: 1px solid #ddd; padding: 10px; max-height: 300px; overflow-y: auto;"><div id="noAttractionsMsg">No attractions added yet. Use the controls below to add attractions.</div><div id="attractionsList"></div></div>'
                                },
                                {
                                    type: 'text',
                                    id: 'attractionName',
                                    label: 'Attraction Name'
                                },
                                {
                                    type: 'textarea',
                                    id: 'attractionDesc',
                                    label: 'Description',
                                    rows: 3
                                },
                                {
                                    type: 'file',
                                    id: 'attractionImage',
                                    label: 'Image (placeholder in this demo)',
                                    size: 38
                                },
                                {
                                    type: 'button',
                                    id: 'addAttraction',
                                    label: 'Add Attraction',
                                    onClick: function() {
                                        var dialog = this.getDialog();
                                        var name = dialog.getValueOf('tab1', 'attractionName');
                                        var desc = dialog.getValueOf('tab1', 'attractionDesc');
                                        
                                        if (!name) {
                                            alert('Please enter an attraction name.');
                                            return;
                                        }
                                        
                                        // Add to list in dialog
                                        var noMsg = document.getElementById('noAttractionsMsg');
                                        if (noMsg) noMsg.style.display = 'none';
                                        
                                        var list = document.getElementById('attractionsList');
                                        var itemId = 'attraction-' + Date.now();
                                        var item = document.createElement('div');
                                        item.id = itemId;
                                        item.className = 'attraction-item';
                                        item.style.border = '1px solid #eee';
                                        item.style.padding = '8px';
                                        item.style.marginBottom = '8px';
                                        item.style.backgroundColor = '#f9f9f9';
                                        
                                        item.innerHTML = `
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <strong>${name}</strong>
                                                <button type="button" style="border: none; background: none; color: red; cursor: pointer;" onclick="document.getElementById('${itemId}').remove(); if (document.getElementById('attractionsList').children.length === 0) document.getElementById('noAttractionsMsg').style.display = 'block';">×</button>
                                            </div>
                                            <div style="margin-top: 5px; font-size: 12px; color: #666;">${desc || 'No description'}</div>
                                        `;
                                        
                                        list.appendChild(item);
                                        
                                        // Clear inputs
                                        dialog.setValueOf('tab1', 'attractionName', '');
                                        dialog.setValueOf('tab1', 'attractionDesc', '');
                                    }
                                }
                            ]
                        }
                    ],
                    onOk: function() {
                        var dialog = this;
                        var title = dialog.getValueOf('tab1', 'galleryTitle');
                        var layout = dialog.getValueOf('tab1', 'galleryLayout');
                        
                        // Get attractions from the list
                        var attractionsList = document.getElementById('attractionsList');
                        var attractions = [];
                        
                        if (attractionsList) {
                            var items = attractionsList.querySelectorAll('.attraction-item');
                            
                            for (var i = 0; i < items.length; i++) {
                                var nameElem = items[i].querySelector('strong');
                                var descElem = items[i].querySelector('div[style*="color: #666"]');
                                
                                if (nameElem) {
                                    attractions.push({
                                        name: nameElem.textContent,
                                        description: descElem ? descElem.textContent : ''
                                    });
                                }
                            }
                        }
                        
                        // If no attractions, add sample data
                        if (attractions.length === 0) {
                            attractions = [
                                { name: 'Sample Attraction 1', description: 'Description of the first attraction.' },
                                { name: 'Sample Attraction 2', description: 'Description of the second attraction.' },
                                { name: 'Sample Attraction 3', description: 'Description of the third attraction.' }
                            ];
                        }
                        
                        // Generate gallery HTML based on layout
                        var galleryHtml = `<div class="attractions-gallery gallery-layout-${layout}">
                            <h3>${title}</h3>
                            <div class="attractions-container">`;
                        
                        // Different layouts
                        if (layout === 'slider') {
                            galleryHtml += '<div class="attractions-slider">';
                            attractions.forEach(function(attraction) {
                                galleryHtml += `
                                    <div class="attraction-slide">
                                        <div class="attraction-image"><img src="placeholder.jpg" alt="${attraction.name}" /></div>
                                        <h4>${attraction.name}</h4>
                                        <p>${attraction.description}</p>
                                    </div>
                                `;
                            });
                            galleryHtml += '</div>';
                        } else if (layout === 'list') {
                            galleryHtml += '<ul class="attractions-list">';
                            attractions.forEach(function(attraction) {
                                galleryHtml += `
                                    <li class="attraction-item">
                                        <div class="attraction-image"><img src="placeholder.jpg" alt="${attraction.name}" /></div>
                                        <div class="attraction-details">
                                            <h4>${attraction.name}</h4>
                                            <p>${attraction.description}</p>
                                        </div>
                                    </li>
                                `;
                            });
                            galleryHtml += '</ul>';
                        } else {
                            // Grid layout
                            var columns = layout === 'grid-2' ? 2 : 3;
                            galleryHtml += `<div class="attractions-grid" style="display: grid; grid-template-columns: repeat(${columns}, 1fr); gap: 20px;">`;
                            attractions.forEach(function(attraction) {
                                galleryHtml += `
                                    <div class="attraction-card">
                                        <div class="attraction-image"><img src="placeholder.jpg" alt="${attraction.name}" style="width:100%;" /></div>
                                        <h4>${attraction.name}</h4>
                                        <p>${attraction.description}</p>
                                    </div>
                                `;
                            });
                            galleryHtml += '</div>';
                        }
                        
                        galleryHtml += `</div>
                        </div>`;
                        
                        // Insert gallery
                        editor.insertHtml(galleryHtml);
                    }
                };
                
                editor.openDialog('attractionsGalleryDialog', function(dialog) {
                    return dialogDefinition;
                });
            }
        });
        
        editor.ui.addButton('AttractionsGallery', {
            label: 'Insert Attractions Gallery',
            command: 'insertAttractionsGallery',
            toolbar: 'travel'
        });
    }
});

// Register all custom plugins
CKEDITOR.plugins.addExternal('LocationMap', '/admin/assets/js/editor-plugins.js');
CKEDITOR.plugins.addExternal('WeatherWidget', '/admin/assets/js/editor-plugins.js');
CKEDITOR.plugins.addExternal('AttractionsGallery', '/admin/assets/js/editor-plugins.js');
