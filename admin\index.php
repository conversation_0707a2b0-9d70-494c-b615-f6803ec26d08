<?php
require_once 'includes/header.php';

// Get dashboard statistics
try {
    // Total posts
    $stmt = $conn->query("SELECT COUNT(*) FROM posts");
    $total_posts = $stmt->fetchColumn();
    
    // Total categories
    $stmt = $conn->query("SELECT COUNT(*) FROM categories");
    $total_categories = $stmt->fetchColumn();
    
    // Total coupons
    $stmt = $conn->query("SELECT COUNT(*) FROM coupons");
    $total_coupons = $stmt->fetchColumn();
    
    // Total stores
    $stmt = $conn->query("SELECT COUNT(*) FROM stores");
    $total_stores = $stmt->fetchColumn();
    
    // Recent posts
    $stmt = $conn->query("SELECT * FROM posts ORDER BY created_at DESC LIMIT 5");
    $recent_posts = $stmt->fetchAll();
    
    // Recent coupons
    $stmt = $conn->query("SELECT c.*, s.name as store_name 
                         FROM coupons c 
                         LEFT JOIN stores s ON c.store_id = s.id 
                         ORDER BY c.created_at DESC LIMIT 5");
    $recent_coupons = $stmt->fetchAll();
    
    // Popular posts
    $stmt = $conn->query("SELECT * FROM posts ORDER BY views DESC LIMIT 5");
    $popular_posts = $stmt->fetchAll();
    
} catch(PDOException $e) {
    $error = 'An error occurred while fetching dashboard data.';
}
?>

<!-- Page Title -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Dashboard</h1>
    <div class="btn-group">
        <button type="button" class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print me-2"></i>Print Report
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="icon text-primary">
                <i class="fas fa-blog"></i>
            </div>
            <div class="number"><?php echo number_format($total_posts); ?></div>
            <div class="label">Total Posts</div>
            <div class="progress mt-2" style="height: 4px;">
                <div class="progress-bar bg-primary" style="width: 100%"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="icon text-success">
                <i class="fas fa-folder"></i>
            </div>
            <div class="number"><?php echo number_format($total_categories); ?></div>
            <div class="label">Categories</div>
            <div class="progress mt-2" style="height: 4px;">
                <div class="progress-bar bg-success" style="width: 100%"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="icon text-warning">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <div class="number"><?php echo number_format($total_coupons); ?></div>
            <div class="label">Active Coupons</div>
            <div class="progress mt-2" style="height: 4px;">
                <div class="progress-bar bg-warning" style="width: 100%"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="icon text-info">
                <i class="fas fa-store"></i>
            </div>
            <div class="number"><?php echo number_format($total_stores); ?></div>
            <div class="label">Partner Stores</div>
            <div class="progress mt-2" style="height: 4px;">
                <div class="progress-bar bg-info" style="width: 100%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row g-4">
    <!-- Recent Posts -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Posts
                </h5>
                <a href="posts.php" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_posts as $post): ?>
                            <tr>
                                <td>
                                    <a href="edit_post.php?id=<?php echo $post['id']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($post['title']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php
                                    $stmt = $conn->prepare("SELECT name FROM categories WHERE id = ?");
                                    $stmt->execute([$post['category_id']]);
                                    $category = $stmt->fetch();
                                    echo $category ? htmlspecialchars($category['name']) : 'Uncategorized';
                                    ?>
                                </td>
                                <td><?php echo date('d M Y', strtotime($post['created_at'])); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $post['status'] == 'published' ? 'success' : ($post['status'] == 'draft' ? 'warning' : 'secondary'); ?>">
                                        <?php echo ucfirst($post['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Coupons -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ticket-alt me-2"></i>Recent Coupons
                </h5>
                <a href="coupons.php" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Store</th>
                                <th>Discount</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_coupons as $coupon): ?>
                            <tr>
                                <td>
                                    <a href="edit_coupon.php?id=<?php echo $coupon['id']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($coupon['code']); ?>
                                    </a>
                                </td>
                                <td><?php echo htmlspecialchars($coupon['store_name']); ?></td>
                                <td>
                                    <?php
                                    if ($coupon['discount_type'] == 'percentage') {
                                        echo $coupon['discount_value'] . '%';
                                    } else {
                                        echo '€' . number_format($coupon['discount_value'], 2);
                                    }
                                    ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $coupon['status'] == 'active' ? 'success' : ($coupon['status'] == 'expired' ? 'danger' : 'secondary'); ?>">
                                        <?php echo ucfirst($coupon['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popular Posts -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-fire me-2"></i>Popular Posts
                </h5>
                <a href="posts.php" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Views</th>
                                <th>Category</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($popular_posts as $post): ?>
                            <tr>
                                <td>
                                    <a href="edit_post.php?id=<?php echo $post['id']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($post['title']); ?>
                                    </a>
                                </td>
                                <td><?php echo number_format($post['views']); ?></td>
                                <td>
                                    <?php
                                    $stmt = $conn->prepare("SELECT name FROM categories WHERE id = ?");
                                    $stmt->execute([$post['category_id']]);
                                    $category = $stmt->fetch();
                                    echo $category ? htmlspecialchars($category['name']) : 'Uncategorized';
                                    ?>
                                </td>
                                <td><?php echo date('d M Y', strtotime($post['created_at'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?> 