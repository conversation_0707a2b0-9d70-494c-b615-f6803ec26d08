<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
requireLogin();

// Get current page number and items per page
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 10;

// Get filter parameters
$status = isset($_GET['status']) ? $_GET['status'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query
$where_conditions = [];
$params = [];

if (!empty($status)) {
    $where_conditions[] = "c.status = :status";
    $params[':status'] = $status;
}

if (!empty($search)) {
    $where_conditions[] = "(c.content LIKE :search OR p.title LIKE :search OR u.username LIKE :search)";
    $params[':search'] = "%$search%";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get total count
$count_sql = "SELECT COUNT(*) as total FROM comments c 
              LEFT JOIN posts p ON c.post_id = p.id 
              LEFT JOIN users u ON c.user_id = u.id 
              $where_clause";
$stmt = $conn->prepare($count_sql);
$stmt->execute($params);
$total_comments = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Calculate total pages
$total_pages = ceil($total_comments / $per_page);
$page = min($page, max(1, $total_pages));

// Get comments with pagination
$offset = ($page - 1) * $per_page;
$sql = "SELECT c.*, p.title as post_title, p.slug as post_slug, 
               u.username, u.avatar as user_avatar,
               COALESCE(pc.content, '') as parent_content,
               COALESCE(pu.username, '') as parent_username
        FROM comments c 
        LEFT JOIN posts p ON c.post_id = p.id 
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN comments pc ON c.parent_id = pc.id
        LEFT JOIN users pu ON pc.user_id = pu.id
        $where_clause 
        ORDER BY c.created_at DESC 
        LIMIT :offset, :per_page";

$stmt = $conn->prepare($sql);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':per_page', $per_page, PDO::PARAM_INT);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Comments</h1>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Search comments...">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                        <option value="spam" <?php echo $status === 'spam' ? 'selected' : ''; ?>>Spam</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Comments Table -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($comments)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                    <h5>No comments found</h5>
                    <p class="text-muted">Try adjusting your filters.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Comment</th>
                                <th>Post</th>
                                <th>Author</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($comments as $comment): ?>
                            <tr>
                                <td>
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <div class="comment-content">
                                                <?php echo nl2br(htmlspecialchars($comment['content'])); ?>
                                            </div>
                                            <?php if (!empty($comment['parent_content'])): ?>
                                                <div class="text-muted small mt-1">
                                                    <i class="fas fa-reply"></i> Reply to <?php echo htmlspecialchars($comment['parent_username']); ?>:
                                                    <?php echo nl2br(htmlspecialchars($comment['parent_content'])); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <a href="<?php echo SITE_URL; ?>/post/<?php echo $comment['post_slug']; ?>" 
                                       target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($comment['post_title']); ?>
                                    </a>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if (!empty($comment['user_avatar'])): ?>
                                            <img src="<?php echo htmlspecialchars($comment['user_avatar']); ?>" 
                                                 alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                                        <?php else: ?>
                                            <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" 
                                                 style="width: 32px; height: 32px;">
                                                <?php echo strtoupper(substr($comment['username'], 0, 1)); ?>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($comment['username']); ?></div>
                                            <?php if (!empty($comment['user_id'])): ?>
                                                <div class="text-muted small">Registered User</div>
                                            <?php else: ?>
                                                <div class="text-muted small">Guest</div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $comment['status'] === 'approved' ? 'success' : 
                                        ($comment['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                        <?php echo ucfirst($comment['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('d M Y H:i', strtotime($comment['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-success approve-comment" 
                                                data-id="<?php echo $comment['id']; ?>"
                                                <?php echo $comment['status'] === 'approved' ? 'disabled' : ''; ?>>
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger spam-comment" 
                                                data-id="<?php echo $comment['id']; ?>"
                                                <?php echo $comment['status'] === 'spam' ? 'disabled' : ''; ?>>
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-primary reply-comment" 
                                                data-id="<?php echo $comment['id']; ?>"
                                                data-username="<?php echo htmlspecialchars($comment['username']); ?>">
                                            <i class="fas fa-reply"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-comment" 
                                                data-id="<?php echo $comment['id']; ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Reply Modal -->
<div class="modal fade" id="replyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reply to Comment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="replyForm">
                    <input type="hidden" id="parent_id" name="parent_id">
                    <input type="hidden" id="post_id" name="post_id">
                    <div class="mb-3">
                        <label for="reply_content" class="form-label">Your Reply</label>
                        <textarea class="form-control" id="reply_content" name="content" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitReply">Submit Reply</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Comment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this comment? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let commentIdToDelete = null;
    const replyModal = new bootstrap.Modal(document.getElementById('replyModal'));

    // Handle approve button click
    $('.approve-comment').click(function() {
        const commentId = $(this).data('id');
        $.ajax({
            url: 'ajax/update_comment_status.php',
            type: 'POST',
            data: { 
                id: commentId,
                status: 'approved'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || 'Failed to approve comment.');
                }
            },
            error: function() {
                alert('An error occurred while approving the comment.');
            }
        });
    });

    // Handle spam button click
    $('.spam-comment').click(function() {
        const commentId = $(this).data('id');
        $.ajax({
            url: 'ajax/update_comment_status.php',
            type: 'POST',
            data: { 
                id: commentId,
                status: 'spam'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || 'Failed to mark comment as spam.');
                }
            },
            error: function() {
                alert('An error occurred while marking the comment as spam.');
            }
        });
    });

    // Handle reply button click
    $('.reply-comment').click(function() {
        const commentId = $(this).data('id');
        const username = $(this).data('username');
        $('#parent_id').val(commentId);
        $('#post_id').val($(this).closest('tr').find('a').attr('href').split('/').pop());
        replyModal.show();
    });

    // Handle reply submission
    $('#submitReply').click(function() {
        const formData = $('#replyForm').serialize();
        $.ajax({
            url: 'ajax/add_comment.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || 'Failed to submit reply.');
                }
            },
            error: function() {
                alert('An error occurred while submitting the reply.');
            }
        });
    });

    // Handle delete button click
    $('.delete-comment').click(function() {
        commentIdToDelete = $(this).data('id');
        $('#deleteModal').modal('show');
    });

    // Handle delete confirmation
    $('#confirmDelete').click(function() {
        if (commentIdToDelete) {
            $.ajax({
                url: 'ajax/delete_comment.php',
                type: 'POST',
                data: { id: commentIdToDelete },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || 'Failed to delete comment.');
                    }
                },
                error: function() {
                    alert('An error occurred while deleting the comment.');
                }
            });
        }
        $('#deleteModal').modal('hide');
    });
});
</script>

<?php include 'includes/footer.php'; ?> 