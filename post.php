<?php
require_once 'includes/config.php';
require_once 'includes/db_queries.php';

// Get post slug from URL
$slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($slug)) {
    header("Location: index.php");
    exit();
}

// Get post details
try {
    $stmt = $conn->prepare("
        SELECT p.*, u.username as author_name, c.name as category_name, c.slug as category_slug, c.icon as category_icon 
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.slug = :slug AND p.status = 'published'
    ");
    $stmt->bindValue(':slug', $slug, PDO::PARAM_STR);
    $stmt->execute();
    $post = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$post) {
        header("Location: index.php");
        exit();
    }

    // Increment view count
    $stmt = $conn->prepare("UPDATE posts SET views = views + 1 WHERE id = :id");
    $stmt->bindValue(':id', $post['id'], PDO::PARAM_INT);
    $stmt->execute();

    // Get related posts
    $stmt = $conn->prepare("
        SELECT p.*, u.username as author_name, c.name as category_name 
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.category_id = :category_id 
        AND p.id != :post_id 
        AND p.status = 'published'
        ORDER BY p.created_at DESC 
        LIMIT 3
    ");
    $stmt->bindValue(':category_id', $post['category_id'], PDO::PARAM_INT);
    $stmt->bindValue(':post_id', $post['id'], PDO::PARAM_INT);
    $stmt->execute();
    $related_posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    error_log("Error fetching post: " . $e->getMessage());
    header("Location: index.php");
    exit();
}

include 'includes/header.php';
?>

<style>
:root {
    --primary-color: #0A0A4A;
    --secondary-color: #FF7F00;
    --text-color: #333;
    --light-text: #666;
    --border-color: #eee;
    --bg-light: #f8f9fa;
    --transition: all 0.3s ease;
}

/* Post Container */
.post-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.post-wrapper {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    align-items: start;
}

/* Post Header */
.post-header {
    margin: -2rem -2rem 2rem;
    padding: 3rem 2rem;
    background: var(--primary-color);
    color: white;
    position: relative;
}

.post-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to bottom, transparent, rgba(10, 10, 74, 0.9));
}

.post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.9);
}

.post-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.post-meta i {
    color: var(--secondary-color);
}

.post-meta a {
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.post-meta a:hover {
    color: var(--secondary-color);
}

.post-title {
    font-size: 2.75rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: white;
}

.post-excerpt {
    font-size: 1.25rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    max-width: 800px;
}

/* Featured Image */
.post-featured-image {
    margin: -2rem -2rem 2rem;
    position: relative;
    border-radius: 0;
    overflow: hidden;
    aspect-ratio: 16/9;
}

.featured-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Post Content */
.post-content {
    font-size: 1.15rem;
    line-height: 1.8;
    color: var(--text-color);
    max-width: 800px;
    margin: 0 auto;
}

.post-content p {
    margin-bottom: 1.75rem;
}

.post-content h2 {
    font-size: 1.8rem;
    margin: 2.5rem 0 1.5rem;
    color: var(--primary-color);
}

.post-content h3 {
    font-size: 1.5rem;
    margin: 2rem 0 1.25rem;
    color: var(--primary-color);
}

.post-content img {
    max-width: 100%;
    height: auto;
    margin: 2rem auto;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.post-content ul, .post-content ol {
    margin: 1.5rem 0;
    padding-left: 1.5rem;
}

.post-content li {
    margin-bottom: 0.75rem;
}

/* Post Tags */
.post-tags {
    margin: 3rem 0;
    padding: 2rem;
    background: var(--bg-light);
    border-radius: 12px;
}

.post-tags h3 {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.tag:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Related Posts */
.related-posts {
    margin-top: 4rem;
    padding-top: 3rem;
    border-top: 1px solid var(--border-color);
}

.related-posts h3 {
    font-size: 1.75rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.article {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.article:hover {
    transform: translateY(-5px);
}

.article-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.article-content {
    padding: 1.5rem;
}

.article-content h3 {
    font-size: 1.25rem;
    margin: 0 0 1rem;
}

.article-content h3 a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.article-content h3 a:hover {
    color: var(--secondary-color);
}

.article-content p {
    color: var(--light-text);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.read-more {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.read-more:hover {
    color: var(--secondary-color);
    gap: 0.75rem;
}

/* Sidebar Improvements */
.sidebar {
    position: sticky;
    top: 2rem;
    background: transparent;
    padding: 0;
    box-shadow: none;
}

.widget {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.widget:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.widget:last-child {
    margin-bottom: 0;
}

.widget-title {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--secondary-color);
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.widget-title i {
    color: var(--secondary-color);
}

/* Popular Posts Widget */
.popular-posts {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.popular-post {
    display: flex;
    gap: 1rem;
    padding-bottom: 1.25rem;
    border-bottom: 1px solid var(--border-color);
}

.popular-post:last-child {
    padding-bottom: 0;
    border-bottom: none;
}

.popular-post-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.popular-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.popular-post:hover .popular-post-image img {
    transform: scale(1.1);
}

.popular-post-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.popular-post-title {
    font-size: 0.95rem;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.popular-post-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.popular-post-title a:hover {
    color: var(--secondary-color);
}

.popular-post-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--light-text);
}

.popular-post-meta span {
    display: flex;
    align-items: center;
    gap: 0.35rem;
}

.popular-post-meta i {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Categories Widget */
.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--bg-light);
    border-radius: 8px;
    text-decoration: none;
    transition: var(--transition);
}

.category-item:hover {
    background: var(--primary-color);
    transform: translateX(5px);
}

.category-name {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
}

.category-item:hover .category-name {
    color: white;
}

.category-name i {
    color: var(--secondary-color);
    transition: var(--transition);
}

.category-item:hover .category-name i {
    color: white;
}

.category-count {
    background: white;
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    transition: var(--transition);
}

.category-item:hover .category-count {
    background: var(--secondary-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .post-wrapper {
        grid-template-columns: 1fr 280px;
        gap: 2rem;
    }

    .popular-post-image {
        width: 70px;
        height: 70px;
    }
}

@media (max-width: 992px) {
    .post-wrapper {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        position: static;
        margin-top: 3rem;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .widget {
        margin-bottom: 0;
    }

    .post-title {
        font-size: 2.25rem;
    }
}

@media (max-width: 768px) {
    .post-container {
        padding: 1rem;
    }
    
    .post-header {
        margin: -1rem -1rem 1.5rem;
        padding: 2rem 1rem;
    }
    
    .post-title {
        font-size: 2rem;
    }
    
    .post-excerpt {
        font-size: 1.1rem;
    }
    
    .post-meta {
        font-size: 0.9rem;
    }
    
    .post-content {
        font-size: 1.1rem;
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
    }

    .sidebar {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .popular-post {
        padding-bottom: 1rem;
    }

    .category-item {
        padding: 0.6rem 1rem;
    }
}

@media (max-width: 480px) {
    .post-title {
        font-size: 1.75rem;
    }
    
    .post-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .post-tags {
        padding: 1.5rem;
    }
    
    .tag {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
}
</style>

<div class="post-container">
    <div class="post-wrapper">
        <div class="content-area">
            <!-- Post Header -->
            <header class="post-header">
                <div class="post-meta">
                    <span class="post-category">
                        <i class="<?php echo !empty($post['category_icon']) ? $post['category_icon'] : 'fas fa-folder'; ?>"></i>
                        <a href="category.php?slug=<?php echo $post['category_slug']; ?>">
                            <?php echo htmlspecialchars($post['category_name']); ?>
                        </a>
                    </span>
                    <span class="post-date">
                        <i class="far fa-calendar-alt"></i>
                        <?php echo formatDate($post['created_at'], 'l, j F Y'); ?>
                    </span>
                    <span class="post-author">
                        <i class="far fa-user"></i>
                        <?php echo htmlspecialchars($post['author_name']); ?>
                    </span>
                    <span class="post-views">
                        <i class="far fa-eye"></i>
                        <?php echo number_format($post['views']); ?> vues
                    </span>
                </div>
                <h1 class="post-title"><?php echo htmlspecialchars($post['title']); ?></h1>
                <?php if (!empty($post['excerpt'])): ?>
                <div class="post-excerpt">
                    <?php echo htmlspecialchars($post['excerpt']); ?>
                </div>
                <?php endif; ?>
            </header>

            <!-- Featured Image -->
            <?php if (!empty($post['featured_image'])): ?>
            <div class="post-featured-image">
                <img src="uploads/posts/<?php echo htmlspecialchars($post['featured_image']); ?>" 
                     alt="<?php echo htmlspecialchars($post['title']); ?>"
                     class="featured-image">
            </div>
            <?php endif; ?>

            <!-- Post Content -->
            <div class="post-content">
                <?php echo $post['content']; ?>
            </div>

            <!-- Post Tags -->
            <?php
            try {
                $stmt = $conn->prepare("
                    SELECT t.name, t.slug 
                    FROM tags t 
                    JOIN post_tags pt ON t.id = pt.tag_id 
                    WHERE pt.post_id = :post_id
                ");
                $stmt->bindValue(':post_id', $post['id'], PDO::PARAM_INT);
                $stmt->execute();
                $tags = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (!empty($tags)):
            ?>
            <div class="post-tags">
                <h3><i class="fas fa-tags"></i> Tags</h3>
                <div class="tags-list">
                    <?php foreach ($tags as $tag): ?>
                    <a href="tag.php?slug=<?php echo $tag['slug']; ?>" class="tag">
                        <i class="fas fa-tag"></i>
                        <?php echo htmlspecialchars($tag['name']); ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php 
                endif;
            } catch(PDOException $e) {
                error_log("Error fetching tags: " . $e->getMessage());
            }
            ?>

            <!-- Related Posts -->
            <?php if (!empty($related_posts)): ?>
            <div class="related-posts">
                <h3><i class="fas fa-newspaper"></i> Articles similaires</h3>
                <div class="articles-grid">
                    <?php foreach ($related_posts as $related_post): ?>
                    <article class="article">
                        <img src="<?php echo !empty($related_post['featured_image']) ? 'uploads/posts/' . htmlspecialchars($related_post['featured_image']) : 'assets/images/default-post.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($related_post['title']); ?>" 
                             class="article-image">
                        <div class="article-content">
                            <h3>
                                <a href="post.php?slug=<?php echo $related_post['slug']; ?>">
                                    <?php echo htmlspecialchars($related_post['title']); ?>
                                </a>
                            </h3>
                            <p><?php echo getExcerpt($related_post['excerpt']); ?></p>
                            <a href="post.php?slug=<?php echo $related_post['slug']; ?>" class="read-more">
                                Lire la suite 
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <aside class="sidebar">
            <?php include 'includes/sidebar.php'; ?>
        </aside>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 