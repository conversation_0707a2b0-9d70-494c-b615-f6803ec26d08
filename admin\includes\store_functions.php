<?php
/**
 * Store management functions
 */

/**
 * Get all stores with pagination
 * @param int $page Current page number
 * @param int $per_page Stores per page
 * @return array Array of stores and total count
 */
function getAllStores($page = 1, $per_page = 10) {
    global $conn;
    
    try {
        // Get total count
        $count_stmt = $conn->query("SELECT COUNT(*) FROM stores");
        $total_stores = $count_stmt->fetchColumn();
        
        // Calculate offset
        $offset = ($page - 1) * $per_page;
        
        // Get stores
        $sql = "SELECT s.*, 
                       COUNT(DISTINCT c.id) as coupon_count
                FROM stores s 
                LEFT JOIN coupons c ON s.id = c.store_id 
                GROUP BY s.id 
                ORDER BY s.created_at DESC 
                LIMIT :limit OFFSET :offset";
                
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Ensure all required fields are present
        foreach ($stores as &$store) {
            $store['name'] = $store['name'] ?? '';
            $store['slug'] = $store['slug'] ?? '';
            $store['description'] = $store['description'] ?? '';
            $store['logo'] = $store['logo'] ?? '';
            $store['website_url'] = $store['website_url'] ?? '';
            $store['affiliate_id'] = $store['affiliate_id'] ?? '';
            $store['status'] = $store['status'] ?? 'inactive';
            $store['created_at'] = $store['created_at'] ?? date('Y-m-d H:i:s');
            $store['coupon_count'] = (int)$store['coupon_count'];
        }
        
        return [
            'stores' => $stores,
            'total' => $total_stores
        ];
        
    } catch (PDOException $e) {
        error_log("Error fetching stores: " . $e->getMessage());
        return [
            'stores' => [],
            'total' => 0
        ];
    }
}

/**
 * Get a store by ID
 * @param int $id Store ID
 * @return array|false Store data or false if not found
 */
function getStoreById($id) {
    global $conn;
    
    try {
        $sql = "SELECT s.*, 
                       COUNT(DISTINCT c.id) as coupon_count
                FROM stores s 
                LEFT JOIN coupons c ON s.id = c.store_id 
                WHERE s.id = :id 
                GROUP BY s.id";
                
        $stmt = $conn->prepare($sql);
        $stmt->execute(['id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching store: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete a store
 * @param int $id Store ID
 * @return boolean Success or failure
 */
function deleteStore($id) {
    global $conn;
    
    try {
        // Get store data first
        $store = getStoreById($id);
        if (!$store) {
            return false;
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Delete related coupons
        $stmt = $conn->prepare("DELETE FROM coupons WHERE store_id = :id");
        $stmt->execute(['id' => $id]);
        
        // Delete the store logo if it exists
        if ($store['logo']) {
            $logo_path = '../../uploads/stores/' . $store['logo'];
            if (file_exists($logo_path)) {
                unlink($logo_path);
            }
        }
        
        // Delete the store
        $stmt = $conn->prepare("DELETE FROM stores WHERE id = :id");
        $result = $stmt->execute(['id' => $id]);
        
        // Commit transaction
        $conn->commit();
        
        return $result;
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        error_log("Error deleting store: " . $e->getMessage());
        return false;
    }
}

/**
 * Validate store data
 * @param array $data Store data to validate
 * @return array Array containing validation status and errors
 */
function validateStoreData($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['name'])) {
        $errors[] = 'Store name is required';
    }
    
    if (empty($data['website_url'])) {
        $errors[] = 'Website URL is required';
    }
    
    // Website URL validation
    if (!empty($data['website_url']) && !filter_var($data['website_url'], FILTER_VALIDATE_URL)) {
        $errors[] = 'Invalid website URL format';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
} 