<?php
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_queries.php';

if (!function_exists('getSetting')) {
function getSetting($key, $default = '') {
    global $conn;
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ? LIMIT 1");
    $stmt->execute([$key]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return $row && $row['setting_value'] !== '' ? $row['setting_value'] : $default;
}
}

$footer_logo = getSetting('footer_logo');
$site_name = getSetting('site_name', 'TRIP NEST');
$social_facebook = getSetting('social_facebook');
$social_instagram = getSetting('social_instagram');
$social_snapchat = getSetting('social_snapchat');
$social_youtube = getSetting('social_youtube');
$social_email = getSetting('social_email');
$social_tiktok = getSetting('social_tiktok');
$social_telegram = getSetting('social_telegram');
$social_pinterest = getSetting('social_pinterest');
$footer_copyright = getSetting('footer_copyright', '© 2013 - 2025 Trip Nest | Offres de vacances & voyages pas chers');
$footer_credit = getSetting('footer_credit', 'Design & Développé par <a href="https://audiencetarget.net/" rel="dofollow" target="_blank">Audience Target</a>');
$country_link_1 = getSetting('country_link_1', 'tripnest.de');
$country_link_1_url = getSetting('country_link_1_url', '#');
$country_link_2 = getSetting('country_link_2', 'tripnest.at');
$country_link_2_url = getSetting('country_link_2_url', '#');

// Fetch dynamic footer HTML from settings table
function getDynamicFooterHtml() {
    global $conn;
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'footer_html' LIMIT 1");
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return $row && !empty($row['setting_value']) ? $row['setting_value'] : null;
}

$dynamicFooter = getDynamicFooterHtml();
if ($dynamicFooter) {
    echo $dynamicFooter;
    return;
}
?>

<!-- Footer Section -->
<footer class="footer">
    <div class="footer-top">
        <div class="container">
            <div class="logo-social">
                <div class="footer-logo">
                    <?php if ($footer_logo): ?>
                        <img src="/uploads/stores/<?php echo htmlspecialchars($footer_logo); ?>" alt="<?php echo htmlspecialchars($site_name); ?>" style="height:60px;max-width:180px;object-fit:contain;">
                    <?php else: ?>
                        <div class="logo-text-footer"><?php echo htmlspecialchars($site_name); ?></div>
                    <?php endif; ?>
                </div>
                <div class="social-links">
                    <h3>SUIVEZ-NOUS</h3>
                    <div class="social-icons">
                        <?php if ($social_facebook): ?><a href="<?php echo htmlspecialchars($social_facebook); ?>" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a><?php endif; ?>
                        <?php if ($social_instagram): ?><a href="<?php echo htmlspecialchars($social_instagram); ?>" aria-label="Instagram"><i class="fab fa-instagram"></i></a><?php endif; ?>
                        <?php if ($social_snapchat): ?><a href="<?php echo htmlspecialchars($social_snapchat); ?>" aria-label="Snapchat"><i class="fab fa-snapchat-ghost"></i></a><?php endif; ?>
                        <?php if ($social_youtube): ?><a href="<?php echo htmlspecialchars($social_youtube); ?>" aria-label="YouTube"><i class="fab fa-youtube"></i></a><?php endif; ?>
                        <?php if ($social_email): ?><a href="mailto:<?php echo htmlspecialchars($social_email); ?>" aria-label="Email"><i class="fas fa-envelope"></i></a><?php endif; ?>
                        <?php if ($social_tiktok): ?><a href="<?php echo htmlspecialchars($social_tiktok); ?>" aria-label="TikTok"><i class="fab fa-tiktok"></i></a><?php endif; ?>
                        <?php if ($social_telegram): ?><a href="<?php echo htmlspecialchars($social_telegram); ?>" aria-label="Telegram"><i class="fab fa-telegram-plane"></i></a><?php endif; ?>
                        <?php if ($social_pinterest): ?><a href="<?php echo htmlspecialchars($social_pinterest); ?>" aria-label="Pinterest"><i class="fab fa-pinterest-p"></i></a><?php endif; ?>
                    </div>
                </div>
                <div class="mascot">
                    <i class="fas fa-binoculars mascot-icon"></i>
                </div>
            </div>

            <div class="footer-columns">
                <!-- Dynamic Pages for Footer -->
                <?php
                // Fetch pages for footer
                $footer_pages = [];
                try {
                    // Check if location column exists
                    $stmt = $conn->query("SHOW COLUMNS FROM pages LIKE 'location'");
                    $column_exists = $stmt->fetch();

                    if ($column_exists) {
                        // If location column exists, filter by location
                        $stmt = $conn->query("SELECT title, slug FROM pages WHERE status='published' AND show_in_menu=1 AND (location='footer' OR location='both') ORDER BY menu_order ASC, created_at DESC");
                        $footer_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    } else {
                        // If location column doesn't exist, don't show any pages in footer
                        $footer_pages = [];
                    }
                } catch (Exception $e) {
                    error_log("Error fetching footer pages: " . $e->getMessage());
                    $footer_pages = [];
                }

                if (!empty($footer_pages)):
                ?>
                <div class="footer-column">
                    <h3>PAGES</h3>
                    <ul>
                        <?php foreach ($footer_pages as $page): ?>
                        <li>
                            <a href="/page/<?php echo htmlspecialchars($page['slug']); ?>">
                                <i class="fas fa-file-alt"></i>
                                <?php echo htmlspecialchars($page['title']); ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <div class="footer-column">
                    <h3>PAYS</h3>
                    <ul>
                        <li>
                            <a href="<?php echo htmlspecialchars($country_link_1_url); ?>">
                                <i class="fas fa-flag flag-icon"></i>
                                <?php echo htmlspecialchars($country_link_1); ?>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo htmlspecialchars($country_link_2_url); ?>">
                                <i class="fas fa-flag flag-icon"></i>
                                <?php echo htmlspecialchars($country_link_2); ?>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="footer-bottom">
        <div class="container">
            <div class="copyright">
                <?php echo $footer_copyright; ?>
            </div>
            <div class="developer-credit">
                <?php echo $footer_credit; ?>
            </div>
        </div>
    </div>
</footer>

<style>
    .footer {
        font-family: 'Arial', sans-serif;
        color: white;
    }

    .footer-top {
        background-color: #0A0A4A;
        padding: 40px 0;
    }

    .container {
        width: 90%;
        max-width: 1200px;
        margin: 0 auto;
    }

    .logo-social {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .footer-logo img {
        height: 80px;
    }

    .logo-text-footer {
        color: #FF7F00;
        font-size: 2.5rem;
        font-weight: bold;
    }

    .logo-text-footer span {
        color: white;
    }

    .social-links h3 {
        text-align: center;
        margin-bottom: 15px;
        font-size: 1rem;
    }

    .social-icons {
        display: flex;
        gap: 15px;
    }

    .social-icons a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 35px;
        height: 35px;
        background-color: white;
        border-radius: 50%;
        color: #FF7F00;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .social-icons a:hover {
        background-color: #FF7F00;
        color: white;
    }

    .mascot img {
        height: 80px;
    }

    .mascot-icon {
        font-size: 3rem;
        color: #FF7F00;
    }

    .footer-columns {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .footer-column {
        flex: 1;
        min-width: 200px;
        margin-bottom: 20px;
    }

    .footer-column h3 {
        margin-bottom: 15px;
        font-size: 1rem;
    }

    .footer-column ul {
        list-style: none;
    }

    .footer-column ul li {
        margin-bottom: 8px;
    }

    .footer-column ul li a {
        color: white;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .footer-column ul li a:hover {
        color: #FF7F00;
    }

    .flag-icon {
        width: auto;
        margin-right: 8px;
        color: #FF7F00;
    }

    .footer-bottom {
        background-color: #0A0A4A;
        padding: 15px 0;
        text-align: center;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }



    .copyright {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
    }

    .developer-credit {
        margin-top: 10px;
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
    }

    .developer-credit a {
        color: #FF7F00;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .developer-credit a:hover {
        color: white;
        text-decoration: underline;
    }

    @media screen and (max-width: 768px) {
        .logo-social {
            flex-direction: column;
            gap: 20px;
        }

        .footer-columns {
            flex-direction: column;
        }

        .footer-column {
            margin-bottom: 30px;
        }

        .developer-credit {
            margin-top: 8px;
            font-size: 0.85rem;
        }
    }

    @media screen and (max-width: 576px) {
        .social-icons {
            flex-wrap: wrap;
            justify-content: center;
        }

        .mascot {
            display: none;
        }
    }
</style>