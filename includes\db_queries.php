<?php
require_once 'config.php';

// Get featured posts for hero section
function getFeaturedPosts($limit = 3) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT p.*, u.username as author_name, c.name as category_name 
            FROM posts p 
            LEFT JOIN users u ON p.author_id = u.id 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.status = 'published' 
            ORDER BY p.created_at DESC 
            LIMIT :limit
        ");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching featured posts: " . $e->getMessage());
        return [];
    }
}

// Get posts by category
function getPostsByCategory($category_slug, $limit = 3) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT p.*, u.username as author_name, c.name as category_name 
            FROM posts p 
            LEFT JOIN users u ON p.author_id = u.id 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.status = 'published' 
            AND c.slug = :category_slug 
            ORDER BY p.created_at DESC 
            LIMIT :limit
        ");
        $stmt->bindValue(':category_slug', $category_slug, PDO::PARAM_STR);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching posts by category: " . $e->getMessage());
        return [];
    }
}

// Get all categories for navigation
function getAllCategories() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT * FROM categories 
            WHERE status = 'active' 
            ORDER BY name ASC
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching categories: " . $e->getMessage());
        return [];
    }
}

// Get active coupons
function getActiveCoupons($limit = 5) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT c.*, s.name as store_name 
            FROM coupons c 
            LEFT JOIN stores s ON c.store_id = s.id 
            WHERE c.status = 'active' 
            AND c.start_date <= NOW() 
            AND c.end_date >= NOW() 
            ORDER BY c.created_at DESC 
            LIMIT :limit
        ");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching active coupons: " . $e->getMessage());
        return [];
    }
}

// Format date
function formatDate($date, $format = 'd/m/Y') {
    return date($format, strtotime($date));
}

// Get post excerpt
function getExcerpt($text, $length = 150) {
    $text = strip_tags($text);
    if (strlen($text) > $length) {
        $text = substr($text, 0, $length) . '...';
    }
    return $text;
}

// Get popular posts for sidebar
function getPopularPosts($limit = 7) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT p.*, u.username as author_name 
            FROM posts p 
            LEFT JOIN users u ON p.author_id = u.id 
            WHERE p.status = 'published' 
            ORDER BY p.views DESC, p.created_at DESC 
            LIMIT :limit
        ");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching popular posts: " . $e->getMessage());
        return [];
    }
}

// Get categories with post count
function getCategoriesWithCount() {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT c.*, COUNT(p.id) as post_count 
            FROM categories c 
            LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published'
            WHERE c.status = 'active'
            GROUP BY c.id 
            ORDER BY c.name ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching categories with count: " . $e->getMessage());
        return [];
    }
}

// Get all active stores with their coupons
function getAllStoresWithCoupons() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT s.*, 
            COUNT(c.id) as total_coupons,
            SUM(CASE WHEN c.status = 'active' THEN 1 ELSE 0 END) as active_coupons
            FROM stores s
            LEFT JOIN coupons c ON s.id = c.store_id
            WHERE s.status = 'active'
            GROUP BY s.id
            ORDER BY s.name ASC
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching stores with coupons: " . $e->getMessage());
        return [];
    }
}

// Get coupons by store ID
function getCouponsByStore($store_id) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT c.*, s.name as store_name, s.logo as store_logo, cat.name as category_name
            FROM coupons c
            JOIN stores s ON c.store_id = s.id
            LEFT JOIN categories cat ON c.category_id = cat.id
            WHERE c.store_id = :store_id AND c.status = 'active'
            AND c.start_date <= NOW() AND c.end_date >= NOW()
            ORDER BY c.created_at DESC
        ");
        $stmt->bindValue(':store_id', $store_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching coupons by store: " . $e->getMessage());
        return [];
    }
}

// Get single coupon details
function getCouponDetails($coupon_id) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT c.*, s.name as store_name, s.logo as store_logo, s.website_url as store_url,
            cat.name as category_name
            FROM coupons c
            JOIN stores s ON c.store_id = s.id
            LEFT JOIN categories cat ON c.category_id = cat.id
            WHERE c.id = :coupon_id AND c.status = 'active'
            AND c.start_date <= NOW() AND c.end_date >= NOW()
        ");
        $stmt->bindValue(':coupon_id', $coupon_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching coupon details: " . $e->getMessage());
        return false;
    }
}

// Track coupon usage
function trackCouponUsage($coupon_id, $user_id = null) {
    global $conn;
    try {
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $stmt = $conn->prepare("
            INSERT INTO coupon_usage (coupon_id, user_id, ip_address) 
            VALUES (:coupon_id, :user_id, :ip_address)
        ");
        $stmt->bindValue(':coupon_id', $coupon_id, PDO::PARAM_INT);
        $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindValue(':ip_address', $ip_address, PDO::PARAM_STR);
        return $stmt->execute();
    } catch(PDOException $e) {
        error_log("Error tracking coupon usage: " . $e->getMessage());
        return false;
    }
}

// Get stores by category
function getStoresByCategory($category_slug) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT DISTINCT s.*, 
            COUNT(c.id) as total_coupons,
            SUM(CASE WHEN c.status = 'active' 
                     AND c.start_date <= NOW() 
                     AND c.end_date >= NOW() 
                THEN 1 ELSE 0 END) as active_coupons
            FROM stores s
            JOIN coupons c ON s.id = c.store_id
            JOIN categories cat ON c.category_id = cat.id
            WHERE s.status = 'active'
            AND cat.slug = :category_slug
            AND c.status = 'active'
            AND c.start_date <= NOW()
            AND c.end_date >= NOW()
            GROUP BY s.id
            ORDER BY s.name ASC
        ");
        $stmt->bindValue(':category_slug', $category_slug, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching stores by category: " . $e->getMessage());
        return [];
    }
}

// Get coupons by store and category
function getCouponsByStoreAndCategory($store_id, $category_slug) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT c.*, s.name as store_name, s.logo as store_logo, 
            cat.name as category_name, cat.icon as category_icon
            FROM coupons c
            JOIN stores s ON c.store_id = s.id
            JOIN categories cat ON c.category_id = cat.id
            WHERE c.store_id = :store_id 
            AND cat.slug = :category_slug
            AND c.status = 'active'
            AND c.start_date <= NOW() 
            AND c.end_date >= NOW()
            ORDER BY c.created_at DESC
        ");
        $stmt->bindValue(':store_id', $store_id, PDO::PARAM_INT);
        $stmt->bindValue(':category_slug', $category_slug, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error fetching coupons by store and category: " . $e->getMessage());
        return [];
    }
}
?> 