<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate required fields
    if (empty($_POST['username']) || empty($_POST['email']) || empty($_POST['password']) || empty($_POST['role'])) {
        throw new Exception('Please fill in all required fields.');
    }

    // Validate email format
    if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format.');
    }

    // Validate password length
    if (strlen($_POST['password']) < 8) {
        throw new Exception('Password must be at least 8 characters long.');
    }

    // Check if username already exists
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
    $stmt->execute([$_POST['username']]);
    if ($stmt->fetchColumn() > 0) {
        throw new Exception('Username already exists.');
    }

    // Check if email already exists
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute([$_POST['email']]);
    if ($stmt->fetchColumn() > 0) {
        throw new Exception('Email already exists.');
    }

    // Handle avatar upload
    $avatar_filename = null;
    if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['avatar'];
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 5 * 1024 * 1024; // 5MB

        // Validate file type
        if (!in_array($file['type'], $allowed_types)) {
            throw new Exception('Invalid file type. Only JPG, PNG, and GIF files are allowed.');
        }

        // Validate file size
        if ($file['size'] > $max_size) {
            throw new Exception('File size exceeds 5MB limit.');
        }

        // Create upload directory if it doesn't exist
        $upload_dir = '../../uploads/avatars/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $avatar_filename = uniqid() . '.' . $extension;
        $target_file = $upload_dir . $avatar_filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $target_file)) {
            throw new Exception('Failed to upload avatar.');
        }
    }

    // Begin transaction
    $conn->beginTransaction();

    // Prepare user data
    $user_data = [
        'username' => trim($_POST['username']),
        'email' => trim($_POST['email']),
        'password' => password_hash($_POST['password'], PASSWORD_DEFAULT),
        'role' => $_POST['role'],
        'first_name' => trim($_POST['first_name'] ?? ''),
        'last_name' => trim($_POST['last_name'] ?? ''),
        'avatar' => $avatar_filename
    ];

    // Insert user
    $sql = "INSERT INTO users (
                username, email, password, role, first_name, last_name, avatar
            ) VALUES (
                :username, :email, :password, :role, :first_name, :last_name, :avatar
            )";

    $stmt = $conn->prepare($sql);
    
    if ($stmt->execute($user_data)) {
        $conn->commit();
        $response['success'] = true;
        $response['message'] = 'User added successfully.';
    } else {
        throw new Exception('Failed to add user.');
    }

} catch (Exception $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // Delete uploaded avatar if exists
    if (!empty($avatar_filename)) {
        $avatar_path = '../../uploads/avatars/' . $avatar_filename;
        if (file_exists($avatar_path)) {
            unlink($avatar_path);
        }
    }
    
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 