<?php
require_once 'config.php';

/**
 * Add a new category
 */
function addCategory($data) {
    global $conn;
    
    try {
        // Validate inputs
        if (empty($data['name'])) {
            throw new Exception('Category name is required.');
        }
        
        // Generate slug if not provided
        $slug = !empty($data['slug']) ? sanitize($data['slug']) : generateSlug($data['name']);
        $name = sanitize($data['name']);
        $parent_id = !empty($data['parent_id']) ? (int)$data['parent_id'] : null;
        $description = sanitize($data['description'] ?? '');
        $status = sanitize($data['status'] ?? 'active');
        
        // Check if slug exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception('A category with this slug already exists.');
        }
        
        // Insert category
        $sql = "INSERT INTO categories (name, slug, parent_id, description, status, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt->execute([$name, $slug, $parent_id, $description, $status])) {
            throw new Exception("Failed to add category.");
        }
        
        return [
            'success' => true,
            'message' => 'Category added successfully.',
            'category_id' => $conn->lastInsertId()
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Update an existing category
 */
function updateCategory($id, $data) {
    global $conn;
    
    try {
        // Validate inputs
        if (empty($data['name'])) {
            throw new Exception('Category name is required.');
        }
        
        $name = sanitize($data['name']);
        $slug = !empty($data['slug']) ? sanitize($data['slug']) : generateSlug($data['name']);
        $icon = !empty($data['icon']) ? sanitize($data['icon']) : 'fas fa-folder';
        $parent_id = !empty($data['parent_id']) ? (int)$data['parent_id'] : null;
        $description = sanitize($data['description'] ?? '');
        $status = sanitize($data['status'] ?? 'active');
        
        // Check if slug exists for other categories
        $stmt = $conn->prepare("SELECT COUNT(*) FROM categories WHERE slug = ? AND id != ?");
        $stmt->execute([$slug, $id]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception('A category with this slug already exists.');
        }
        
        // Update category
        $sql = "UPDATE categories 
                SET name = ?, slug = ?, icon = ?, parent_id = ?, description = ?, status = ?, updated_at = NOW() 
                WHERE id = ?";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt->execute([$name, $slug, $icon, $parent_id, $description, $status, $id])) {
            throw new Exception("Failed to update category.");
        }
        
        return [
            'success' => true,
            'message' => 'Category updated successfully.'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Delete a category
 */
function deleteCategory($id) {
    global $conn;
    
    try {
        // Check if category has posts
        $stmt = $conn->prepare("SELECT COUNT(*) FROM posts WHERE category_id = ?");
        $stmt->execute([$id]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception('Cannot delete category with associated posts.');
        }
        
        // Delete category
        $stmt = $conn->prepare("DELETE FROM categories WHERE id = ?");
        if (!$stmt->execute([$id])) {
            throw new Exception("Failed to delete category.");
        }
        
        return [
            'success' => true,
            'message' => 'Category deleted successfully.'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Get all categories with their parent names and post counts
 * 
 * @return array Array of categories with their details
 */
function getAllCategories() {
    global $conn;
    
    try {
        // Query to get categories with their parent names and post counts
        $sql = "SELECT 
                    c.id,
                    c.name,
                    c.slug,
                    c.icon,
                    c.description,
                    c.parent_id,
                    c.status,
                    p.name as parent_name,
                    (SELECT COUNT(*) FROM posts WHERE category_id = c.id AND status = 'published') as post_count
                FROM 
                    categories c
                LEFT JOIN 
                    categories p ON c.parent_id = p.id
                ORDER BY 
                    c.name ASC";
                    
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch(PDOException $e) {
        error_log("Error in getAllCategories: " . $e->getMessage());
        throw new Exception("Failed to fetch categories");
    }
}

/**
 * Get category by ID
 * 
 * @param int $id Category ID
 * @return array|false Category details or false if not found
 */
function getCategoryById($id) {
    global $conn;
    
    try {
        $sql = "SELECT * FROM categories WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch(PDOException $e) {
        error_log("Error in getCategoryById: " . $e->getMessage());
        throw new Exception("Failed to fetch category");
    }
}

/**
 * Get category by slug
 * 
 * @param string $slug Category slug
 * @return array|false Category details or false if not found
 */
function getCategoryBySlug($slug) {
    global $conn;
    
    try {
        $sql = "SELECT * FROM categories WHERE slug = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$slug]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch(PDOException $e) {
        error_log("Error in getCategoryBySlug: " . $e->getMessage());
        throw new Exception("Failed to fetch category");
    }
}

/**
 * Get active categories
 * 
 * @return array Array of active categories
 */
function getActiveCategories() {
    global $conn;
    
    try {
        $sql = "SELECT 
                    c.*,
                    (SELECT COUNT(*) FROM posts WHERE category_id = c.id AND status = 'published') as post_count
                FROM 
                    categories c
                WHERE 
                    c.status = 'active'
                ORDER BY 
                    c.name ASC";
                    
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch(PDOException $e) {
        error_log("Error in getActiveCategories: " . $e->getMessage());
        throw new Exception("Failed to fetch active categories");
    }
}

/**
 * Check if a category has child categories
 * 
 * @param int $id Category ID
 * @return bool True if category has children, false otherwise
 */
function categoryHasChildren($id) {
    global $conn;
    
    try {
        $sql = "SELECT COUNT(*) FROM categories WHERE parent_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->fetchColumn() > 0;
        
    } catch(PDOException $e) {
        error_log("Error in categoryHasChildren: " . $e->getMessage());
        throw new Exception("Failed to check category children");
    }
}

/**
 * Get child categories of a parent category
 * 
 * @param int $parent_id Parent category ID
 * @return array Array of child categories
 */
function getChildCategories($parent_id) {
    global $conn;
    
    try {
        $sql = "SELECT 
                    c.*,
                    (SELECT COUNT(*) FROM posts WHERE category_id = c.id AND status = 'published') as post_count
                FROM 
                    categories c
                WHERE 
                    c.parent_id = ?
                ORDER BY 
                    c.name ASC";
                    
        $stmt = $conn->prepare($sql);
        $stmt->execute([$parent_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch(PDOException $e) {
        error_log("Error in getChildCategories: " . $e->getMessage());
        throw new Exception("Failed to fetch child categories");
    }
}

/**
 * Update category status
 * 
 * @param int $id Category ID
 * @param string $status New status ('active' or 'inactive')
 * @return bool True on success, false on failure
 */
function updateCategoryStatus($id, $status) {
    global $conn;
    
    try {
        $sql = "UPDATE categories SET status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        
        return $stmt->execute([$status, $id]);
        
    } catch(PDOException $e) {
        error_log("Error in updateCategoryStatus: " . $e->getMessage());
        throw new Exception("Failed to update category status");
    }
}

/**
 * Get active categories for sidebar
 */
function getActiveCategoriesForSidebar() {
    global $conn;
    
    try {
        $stmt = $conn->query("SELECT id, name, slug, parent_id 
                             FROM categories 
                             WHERE status = 'active' 
                             ORDER BY name ASC");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Organize into hierarchical structure
        $tree = [];
        $flat = [];
        
        foreach ($categories as $cat) {
            $flat[$cat['id']] = $cat;
            $flat[$cat['id']]['children'] = [];
        }
        
        foreach ($flat as $id => $cat) {
            if ($cat['parent_id'] && isset($flat[$cat['parent_id']])) {
                $flat[$cat['parent_id']]['children'][] = &$flat[$id];
            } else {
                $tree[] = &$flat[$id];
            }
        }
        
        return $tree;
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Create a new category
 */
function createCategory($data) {
    global $conn;
    
    try {
        $sql = "INSERT INTO categories (name, slug, description, status, created_at, updated_at) 
                VALUES (:name, :slug, :description, :status, NOW(), NOW())";
        
        $stmt = $conn->prepare($sql);
        return $stmt->execute([
            ':name' => $data['name'],
            ':slug' => $data['slug'],
            ':description' => $data['description'],
            ':status' => $data['status']
        ]);
        
    } catch (PDOException $e) {
        error_log("Error in createCategory: " . $e->getMessage());
        return false;
    }
}

/**
 * Validate category data
 */
function validateCategoryData($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['name'])) {
        $errors[] = 'Category name is required.';
    }
    
    if (empty($data['slug'])) {
        $errors[] = 'Category slug is required.';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * Check if a category slug already exists
 */
function isCategorySlugExists($slug, $exclude_id = null) {
    global $conn;
    
    try {
        $sql = "SELECT COUNT(*) as count FROM categories WHERE slug = :slug";
        $params = [':slug' => $slug];
        
        if ($exclude_id) {
            $sql .= " AND id != :id";
            $params[':id'] = $exclude_id;
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;
        
    } catch (PDOException $e) {
        error_log("Error in isCategorySlugExists: " . $e->getMessage());
        return false;
    }
} 