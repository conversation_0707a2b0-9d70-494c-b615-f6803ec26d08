<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Handle menu order update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['order_id'], $_POST['menu_order'])) {
    $oid = (int)$_POST['order_id'];
    $order = (int)$_POST['menu_order'];
    $stmt = $conn->prepare("UPDATE pages SET menu_order=? WHERE id=?");
    $stmt->execute([$order, $oid]);
    setMessage('success', 'Menu order updated!');
    header('Location: pages.php');
    exit;
}

// Fetch all pages (ordered by menu_order, then created_at)
$stmt = $conn->query("SELECT * FROM pages ORDER BY menu_order ASC, created_at DESC");
$pages = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Pages</h1>
        <div>
            <a href="add_location_column.php" class="btn btn-warning me-2"><i class="fas fa-database"></i> Fix Database</a>
            <a href="add_page.php" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Page</a>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <?php if (empty($pages)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5>No pages found</h5>
                    <a href="add_page.php" class="btn btn-primary mt-3"><i class="fas fa-plus"></i> Add New Page</a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Slug</th>
                                <th>Status</th>
                                <th>Show in Menu</th>
                                <th>Location</th>
                                <th>Order</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pages as $page): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($page['title']); ?></td>
                                    <td><?php echo htmlspecialchars($page['slug']); ?></td>
                                    <td><span class="badge bg-<?php echo $page['status'] === 'published' ? 'success' : 'secondary'; ?>"><?php echo ucfirst($page['status']); ?></span></td>
                                    <td><?php echo $page['show_in_menu'] ? 'Yes' : 'No'; ?></td>
                                    <td>
                                        <?php
                                        if (isset($page['location'])) {
                                            $location = $page['location'];
                                            if ($location === 'header') echo 'Header';
                                            else if ($location === 'footer') echo 'Footer';
                                            else if ($location === 'both') echo 'Both';
                                            else echo 'Header';
                                        } else {
                                            echo 'Header (default)';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <form method="post" action="pages.php" style="display:inline-block;width:60px;">
                                            <input type="hidden" name="order_id" value="<?php echo $page['id']; ?>">
                                            <input type="number" name="menu_order" value="<?php echo (int)$page['menu_order']; ?>" style="width:50px;" min="0">
                                            <button type="submit" class="btn btn-sm btn-link p-0" title="Save Order"><i class="fas fa-save"></i></button>
                                        </form>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($page['created_at'])); ?></td>
                                    <td>
                                        <a href="edit_page.php?id=<?php echo $page['id']; ?>" class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i></a>
                                        <a href="delete_page.php?id=<?php echo $page['id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Delete this page?');"><i class="fas fa-trash"></i></a>
                                        <a href="/page/<?php echo htmlspecialchars($page['slug']); ?>" class="btn btn-sm btn-outline-secondary" target="_blank"><i class="fas fa-eye"></i></a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
