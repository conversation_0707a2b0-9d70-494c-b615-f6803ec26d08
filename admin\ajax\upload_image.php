<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized access']));
}

// Create uploads directory if it doesn't exist
$upload_dir = '../../uploads/posts/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Handle file upload
if (isset($_FILES['file']) && $_FILES['file']['error'] === 0) {
    $result = uploadFile($_FILES['file'], $upload_dir);
    if ($result) {
        echo json_encode([
            'location' => SITE_URL . '/uploads/posts/' . $result
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'error' => 'Failed to upload file'
        ]);
    }
} else {
    http_response_code(400);
    echo json_encode([
        'error' => 'No file uploaded'
    ]);
} 