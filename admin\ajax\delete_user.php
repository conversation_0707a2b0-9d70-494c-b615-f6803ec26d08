<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate user ID
    if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
        throw new Exception('Invalid user ID.');
    }

    $user_id = (int)$_POST['id'];

    // Prevent self-deletion
    if ($user_id === $_SESSION['admin_id']) {
        throw new Exception('You cannot delete your own account.');
    }

    // Check if user exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    if (!$user) {
        throw new Exception('User not found.');
    }

    // Begin transaction
    $conn->beginTransaction();

    // Delete user's avatar if exists
    if (!empty($user['avatar'])) {
        $avatar_path = '../../uploads/avatars/' . $user['avatar'];
        if (file_exists($avatar_path)) {
            unlink($avatar_path);
        }
    }

    // Delete user
    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
    if ($stmt->execute([$user_id])) {
        $conn->commit();
        $response['success'] = true;
        $response['message'] = 'User deleted successfully.';
    } else {
        throw new Exception('Failed to delete user.');
    }

} catch (Exception $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 