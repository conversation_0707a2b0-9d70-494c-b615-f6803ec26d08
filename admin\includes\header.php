<?php
require_once 'config.php';
requireLogin();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - <?php echo SITE_NAME; ?></title>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo ADMIN_URL; ?>/assets/css/admin.css" rel="stylesheet">
    <link href="<?php echo ADMIN_URL; ?>/assets/css/editor-styles.css" rel="stylesheet">

    <!-- JavaScript Dependencies -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- CKEditor 4 (more stable with better features) -->
    <script src="https://cdn.ckeditor.com/4.20.0/full-all/ckeditor.js"></script>

    <!-- Custom CSS for CKEditor -->
    <style>
        /* Ensure the editor container has enough height */
        .ck-editor__editable_inline {
            min-height: 400px;
            max-height: 800px;
            padding: 1.5em !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            background-color: #fff !important;
            color: #333 !important;
            font-size: 1em !important;
            line-height: 1.6 !important;
        }

        /* Style for the toolbar */
        .ck.ck-toolbar {
            border-radius: 4px !important;
            background: #f8f9fa !important;
            border: 1px solid #ddd !important;
            margin-bottom: 10px !important;
        }

        /* Style for the content area */
        .ck.ck-editor__main>.ck-editor__editable {
            border-radius: 4px !important;
        }

        /* Style for the focused editor */
        .ck.ck-editor__editable:not(.ck-editor__nested-editable).ck-focused {
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            border-color: #80bdff !important;
        }

        /* Toolbar container */
        #toolbar-container {
            margin-bottom: 10px;
        }

        /* Style for toolbar buttons */
        .ck.ck-button {
            padding: 6px 10px !important;
            border-radius: 3px !important;
            font-size: 0.9em !important;
        }

        .ck.ck-button:hover {
            background: #e9ecef !important;
        }

        /* Style for active toolbar buttons */
        .ck.ck-button.ck-on {
            background: #e9ecef !important;
            color: #0A0A4A !important;
        }

        /* Style for dropdown panels */
        .ck.ck-dropdown__panel {
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        }

        /* Style for headings in the editor */
        .ck-content h1, .ck-content h2, .ck-content h3,
        .ck-content h4, .ck-content h5, .ck-content h6 {
            margin-top: 1em !important;
            margin-bottom: 0.5em !important;
            color: #0A0A4A !important;
        }

        /* Style for links in the editor */
        .ck-content a {
            color: #0A0A4A !important;
            text-decoration: underline !important;
        }

        /* Style for tables in the editor */
        .ck-content table {
            border-collapse: collapse !important;
            width: 100% !important;
            margin: 1em 0 !important;
        }

        .ck-content table td, .ck-content table th {
            border: 1px solid #ddd !important;
            padding: 8px !important;
        }

        .ck-content table th {
            background-color: #f8f9fa !important;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <?php include 'sidebar.php'; ?>

        <!-- Page Content -->
        <div class="content">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-bars"></i>
                    </button>

                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i> <?php echo $_SESSION['admin_username']; ?>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="<?php echo ADMIN_URL; ?>/profile.php">Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo ADMIN_URL; ?>/logout.php">Logout</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo ADMIN_URL; ?>/logout.php" title="Logout">
                                    <i class="fas fa-sign-out-alt"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <div class="container-fluid">
                <?php
                $message = getMessage();
                if ($message): ?>
                    <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message['text']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
