-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Jul 15, 2025 at 12:05 PM
-- Server version: 10.11.10-MariaDB-log
-- PHP Version: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u698281066_tripnest`
--

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-folder',
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `icon`, `description`, `parent_id`, `image`, `status`, `created_at`, `updated_at`) VALUES
(5, 'Voyage', 'voyage', 'fas fa-hiking', '', NULL, NULL, 'active', '2025-03-25 20:06:18', '2025-03-25 20:12:25'),
(6, 'Excursions en ville', 'excursions-en-ville', 'fas fa-city', '', NULL, NULL, 'active', '2025-03-25 21:02:33', '2025-03-25 21:02:33');

-- --------------------------------------------------------

--
-- Table structure for table `comments`
--

CREATE TABLE `comments` (
  `id` int(11) NOT NULL,
  `post_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `content` text NOT NULL,
  `status` enum('pending','approved','spam') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `coupons`
--

CREATE TABLE `coupons` (
  `id` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `discount_type` enum('percentage','fixed') NOT NULL,
  `discount_value` decimal(10,2) NOT NULL,
  `store_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `usage_limit` int(11) DEFAULT NULL,
  `used_count` int(11) DEFAULT 0,
  `minimum_purchase` decimal(10,2) DEFAULT NULL,
  `status` enum('active','inactive','expired') DEFAULT 'active',
  `affiliate_link` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `coupons`
--

INSERT INTO `coupons` (`id`, `code`, `description`, `discount_type`, `discount_value`, `store_id`, `category_id`, `start_date`, `end_date`, `usage_limit`, `used_count`, `minimum_purchase`, `status`, `affiliate_link`, `created_at`, `updated_at`) VALUES
(13, 'Code appliqué', 'Séjour en Résidence premium L\'Hévana à partir de 840€', 'fixed', 840.00, 5, 5, '2025-05-11 00:00:00', '2027-01-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.tkqlhce.com/click-101436905-10665515', '2025-05-11 11:34:24', '2025-05-11 11:34:24'),
(14, 'SUMMER25', 'Pierre et Vacances Code Promo', 'percentage', 5.00, 5, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.tkqlhce.com/click-101436905-10665515', '2025-05-11 11:35:07', '2025-05-11 11:35:07'),
(15, 'NEWSY25PV', '50€ Code de réduction Pierre & Vacances', 'fixed', 50.00, 5, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.tkqlhce.com/click-101436905-10665515', '2025-05-11 11:35:35', '2025-05-11 11:35:35'),
(16, 'OBIZY25PV', '5% Code de remise Pierre & Vacances', 'percentage', 5.00, 5, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.tkqlhce.com/click-101436905-10665515', '2025-05-11 11:36:06', '2025-05-11 11:36:06'),
(17, 'WELCOME', '5% Pierre & Vacances Code Promo', 'percentage', 5.00, 5, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.tkqlhce.com/click-101436905-10665515', '2025-05-11 11:36:26', '2025-05-11 11:36:26'),
(18, 'Code automatique appliqué', 'Offre de printemps: Votre séjour à partir de 279€', 'fixed', 279.00, 4, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.jdoqocy.com/click-101436905-12470051', '2025-05-11 11:44:16', '2025-05-11 11:44:16'),
(19, 'Remise appliquée', '30€ de réduction sur Sunparks', 'fixed', 30.00, 4, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.jdoqocy.com/click-101436905-12470051', '2025-05-11 11:45:58', '2025-05-11 11:45:58'),
(20, 'Remise appliquée auto', '20% de réduction sur Sunparks', 'percentage', 20.00, 4, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.jdoqocy.com/click-101436905-12470051', '2025-05-11 11:46:42', '2025-05-11 11:46:42'),
(21, 'Remise appliquée par lien', 'Last Minutes: 409€ Sunparks Code de remise', 'percentage', 10.00, 4, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.jdoqocy.com/click-101436905-12470051', '2025-05-11 11:47:38', '2025-05-11 11:47:38'),
(22, 'Code appliqué par lien', 'Vacances de Pâques: 20% Sunparks Promo Code', 'percentage', 20.00, 4, 5, '2025-05-11 00:00:00', '2027-05-11 00:00:00', NULL, 0, NULL, 'active', 'https://www.jdoqocy.com/click-101436905-12470051', '2025-05-11 11:49:05', '2025-05-11 11:49:48');

-- --------------------------------------------------------

--
-- Table structure for table `coupon_usage`
--

CREATE TABLE `coupon_usage` (
  `id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `used_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pages`
--

CREATE TABLE `pages` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `status` enum('published','draft') DEFAULT 'published',
  `show_in_menu` tinyint(1) DEFAULT 1,
  `location` enum('header','footer','both') DEFAULT 'header',
  `menu_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pages`
--

INSERT INTO `pages` (`id`, `title`, `slug`, `content`, `meta_title`, `meta_description`, `status`, `show_in_menu`, `location`, `menu_order`, `created_at`, `updated_at`) VALUES
(1, 'À propos de nous', 'A-propos-de-nous', '<p><strong>Explorez plus. D&eacute;pensez moins. Voyagez intelligemment.</strong></p>\r\n\r\n<p>Bienvenue sur <strong>Tripnest.fr</strong>, votre source de confiance pour des bons plans voyage, de l&rsquo;inspiration et des escapades abordables. Nous croyons que d&eacute;couvrir le monde ne devrait pas &ecirc;tre un luxe &mdash; c&rsquo;est pourquoi nous nous engageons &agrave; vous proposer les meilleures offres de voyage en France et au-del&agrave;.</p>\r\n\r\n<h3>Qui sommes-nous?</h3>\r\n\r\n<p><strong>Tripnest.fr</strong> est exploit&eacute; par GROWFINITY MARKETING, une soci&eacute;t&eacute; de marketing digital enregistr&eacute;e au Royaume-Uni, passionn&eacute;e de voyage et d&rsquo;innovation. Notre &eacute;quipe de cr&eacute;ateurs de contenu, de chasseurs de bons plans et d&#39;experts en affiliation travaille chaque jour pour vous offrir des offres de voyage imbattables, s&eacute;lectionn&eacute;es avec soin.</p>\r\n\r\n<h3><strong>Notre mission</strong></h3>\r\n\r\n<p>Nous souhaitons permettre &agrave; chacun de voyager, quel que soit son budget. En vous connectant &agrave; nos partenaires de confiance via des offres exclusives, nous rendons les voyages plus accessibles, abordables et inspirants.</p>\r\n\r\n<p><strong>Nos engagements:</strong></p>\r\n\r\n<ul>\r\n	<li>\r\n	<p>Proposer des offres v&eacute;rifi&eacute;es, choisies avec soin.</p>\r\n	</li>\r\n	<li>\r\n	<p>Mettre en avant des partenaires fiables et reconnus.</p>\r\n	</li>\r\n	<li>\r\n	<p>Offrir une plateforme gratuite, transparente et centr&eacute;e sur l&rsquo;utilisateur.</p>\r\n	</li>\r\n</ul>\r\n\r\n<h3><strong>Ce que nous proposons</strong></h3>\r\n\r\n<p>Sur Tripnest.fr, vous trouverez :</p>\r\n\r\n<ul>\r\n	<li>\r\n	<p>Des promotions exclusives de grandes marques comme <strong>Pierre &amp; Vacances</strong>, <strong>Sunparks</strong> et bien d&rsquo;autres.</p>\r\n	</li>\r\n	<li>\r\n	<p>Des offres sur les s&eacute;jours, week-ends, villages vacances et parcs d&rsquo;attractions.</p>\r\n	</li>\r\n	<li>\r\n	<p>Des conseils et de l&rsquo;inspiration voyage sp&eacute;cialement pens&eacute;s pour les voyageurs fran&ccedil;ais.</p>\r\n	</li>\r\n</ul>\r\n\r\n<p>Toutes nos offres sont mises &agrave; jour r&eacute;guli&egrave;rement et redirigent vers les sites officiels des prestataires pour une r&eacute;servation en toute s&eacute;curit&eacute;.</p>\r\n\r\n<h3><strong>Notre vision</strong></h3>\r\n\r\n<p>Le voyage est bien plus qu&rsquo;un luxe &mdash; c&rsquo;est une opportunit&eacute; de grandir, de s&rsquo;ouvrir et de s&rsquo;enrichir. Chez <strong>Tripnest.fr</strong>, nous construisons une communaut&eacute; de voyageurs &agrave; la recherche d&rsquo;exp&eacute;riences authentiques, abordables et enrichissantes.</p>\r\n\r\n<p>Que vous planifiez votre prochaine escapade ou recherchiez de l&rsquo;inspiration, Tripnest.fr est votre point de d&eacute;part.</p>', 'Tripnest.fr – Inspiration & idées pour vos prochains voyages', 'Explorez Tripnest.fr : idées de destinations, conseils pratiques et inspirations voyage en France, Europe et à travers le monde', 'published', 1, 'footer', 11, '2025-05-01 00:26:16', '2025-06-02 15:38:01'),
(2, 'Contactez-nous', 'Contactez-nous', '<p>Nous sommes &agrave; votre &eacute;coute !</p>\r\n\r\n<p>Vous avez une question &agrave; propos de l&rsquo;une de nos offres de voyage, une suggestion, une remarque, ou vous avez constat&eacute; une erreur sur notre site ? N&rsquo;h&eacute;sitez pas &agrave; nous contacter.</p>\r\n\r\n<h3>Demandes g&eacute;n&eacute;rales</h3>\r\n\r\n<p>Pour toute question, suggestion ou probl&egrave;me li&eacute; au site, veuillez nous &eacute;crire &agrave; :<br />\r\n<ins><EMAIL></ins></p>\r\n\r\n<h3>Partenariats &amp; collaborations</h3>\r\n\r\n<p>Vous &ecirc;tes une marque de voyage ou une agence et souhaitez publier vos offres sur Tripnest ?<br />\r\nContactez-nous pour discuter d&rsquo;un partenariat affili&eacute; ou d&rsquo;une collaboration m&eacute;dia :<br />\r\n<ins><EMAIL></ins></p>\r\n\r\n<h3>D&eacute;lai de r&eacute;ponse</h3>\r\n\r\n<p>Nous faisons de notre mieux pour r&eacute;pondre &agrave; tous les messages dans un d&eacute;lai de 24 &agrave; 48 heures (jours ouvr&eacute;s).</p>\r\n\r\n<h3>&Agrave; noter</h3>\r\n\r\n<p>Tripnest.fr est une plateforme ind&eacute;pendante sp&eacute;cialis&eacute;e dans les bons plans voyage. Nous ne g&eacute;rons pas les r&eacute;servations. Pour toute question relative &agrave; une r&eacute;servation, merci de contacter directement le prestataire concern&eacute;.</p>', 'Contactez Tripnest.fr – Questions, Partenariats & Support', 'Besoin d’aide ou envie de collaborer avec Tripnest ? Contactez notre équipe pour toute question, suggestion ou opportunité commerciale. Nous sommes à votre écoute !', 'published', 1, 'footer', 0, '2025-05-08 08:51:56', '2025-05-10 18:30:55'),
(3, 'Imprimer', 'imprimer', '<p><strong>Tripnest.fr</strong> is operated by:</p>\r\n\r\n<p><strong>Company Name:</strong><br />\r\nGrowfinity Marketing</p>\r\n\r\n<p><strong>Address:&nbsp;</strong></p>\r\n\r\n<p><strong>O</strong>ffice No 2, 3rd Floor, Awais Complex 13 Fane Road, Lahore</p>\r\n\r\n<p><strong>Phone:</strong><br />\r\n+92 328 4556 469</p>\r\n\r\n<p><strong>Email:</strong></p>\r\n\r\n<p><EMAIL></p>\r\n\r\n<p><strong>Managing Director:</strong><br />\r\n<ins>Arshad Mehmood</ins></p>\r\n\r\n<p><ins><EMAIL></ins></p>\r\n\r\n<p><strong>Responsible for content according to &sect; 55 Abs. 2 RStV (Germany):</strong><br />\r\nArshad Mehmood<br />\r\n260 Sir Syed Street, Al Hamd Colony<br />\r\nIchra, Lahore, Punjab 54000<br />\r\nPakistan</p>\r\n\r\n<p><strong>Clause de non-responsabilit&eacute;</strong></p>\r\n\r\n<p>Malgr&eacute; un contr&ocirc;le minutieux du contenu, nous n&#39;assumons aucune responsabilit&eacute; pour le contenu des liens externes. Les exploitants des pages li&eacute;es sont seuls responsables de leur contenu.</p>\r\n\r\n<p><strong>Copyright</strong></p>\r\n\r\n<p>Tous les logos, noms de marques et marques d&eacute;pos&eacute;es affich&eacute;s sur Tripnest.fr appartiennent &agrave; leurs propri&eacute;taires respectifs. Les images des produits et le mat&eacute;riel promotionnel proviennent directement des magasins officiels ou des r&eacute;seaux affili&eacute;s et sont utilis&eacute;s &agrave; des fins d&#39;information et de promotion uniquement.</p>', 'Imprimer | Tripnest.fr', 'Consultez les mentions légales de Tripnest.fr. Informations sur l’éditeur du site, les droits d’auteur, la responsabilité et les conditions d’utilisation du contenu.', 'published', 1, 'footer', 0, '2025-05-08 09:25:38', '2025-06-02 15:36:21');

-- --------------------------------------------------------

--
-- Table structure for table `posts`
--

CREATE TABLE `posts` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `excerpt` text DEFAULT NULL,
  `featured_image` varchar(255) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `author_id` int(11) NOT NULL,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` varchar(255) DEFAULT NULL,
  `views` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `posts`
--

INSERT INTO `posts` (`id`, `title`, `slug`, `content`, `excerpt`, `featured_image`, `category_id`, `author_id`, `status`, `meta_title`, `meta_description`, `meta_keywords`, `views`, `created_at`, `updated_at`) VALUES
(7, 'Discover Pierre et Vacances: The Smart Way to Travel Across Europe', 'discover-pierre-et-vacances-the-smart-way-to-travel-across-europe', '<p>When it comes to planning the perfect European getaway, finding the right balance between comfort, flexibility, and affordability can be a challenge. That’s where <strong>Pierre &amp; Vacances</strong> steps in — a trusted name in holiday accommodation for over 50 years.</p><h3>Who is Pierre &amp; Vacances?</h3><p>Pierre &amp; Vacances is a leading European holiday provider, offering a wide range of self-catered apartments, residences, and holiday villages in some of the most sought-after destinations across France, Spain, Italy, Portugal, and beyond. Whether you\'re dreaming of a sunny beach holiday, a mountain adventure, or a city escape, Pierre &amp; Vacances has something for everyone.</p><h3>What Makes Them Unique?</h3><p>Unlike traditional hotels, Pierre &amp; Vacances specializes in <strong>flexible, apartment-style accommodations</strong> — ideal for families, groups of friends, or couples who want the freedom to enjoy their holiday at their own pace. Each residence is fully equipped, giving guests the option to cook, relax, and feel at home while traveling.</p><p>Plus, many of their resorts offer on-site amenities like pools, kids\' clubs, sports facilities, and even wellness areas — all designed to enhance the holiday experience without breaking the bank.</p><h3>Great Value with Regular Deals</h3><p>One of the biggest perks of booking with Pierre &amp; Vacances is the <strong>regular discounts and last-minute deals</strong> they offer throughout the year. From early booking offers to seasonal sales, travelers can save significantly — especially when planning longer stays or booking for multiple guests.</p><p>💡 <strong>Tip:</strong> Keep an eye out for promotions like up to <strong>30% off summer holidays</strong> or <strong>free nights for longer stays</strong>.</p><h3>Sustainability at Heart</h3><p>Pierre &amp; Vacances is also committed to <strong>eco-friendly tourism</strong>, with many of their sites awarded for sustainable practices. From reducing energy consumption to supporting local communities, they’re working hard to make travel more responsible — and that’s something we can all get behind.</p><h3>Why Book with Pierre &amp; Vacances?</h3><ul><li>Over 380 properties across Europe</li><li>Ideal for families, couples, and group travel</li><li>Competitive pricing with regular deals</li><li>Stunning locations near beaches, mountains, and cities</li><li>Option to add services like breakfast, cleaning, or parking</li><li>Trusted by millions of travelers</li></ul><h3>Final Thoughts</h3><p>If you\'re planning your next escape and want a mix of comfort, convenience, and cost-effectiveness, <strong>Pierre &amp; Vacances is well worth considering</strong>. With flexible stays, beautiful locations, and great value offers, they make European travel simple and enjoyable.</p><p><br></p><p><img src=\"data:image/png;base64,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\"></p>', 'That’s where Pierre &amp; Vacances steps in — a trusted name in holiday accommodation for over 50 years.', '684fff240d29c.png', 6, 1, 'published', 'Discover Pierre et Vacances:  Travel Across Europe', 'That’s where Pierre &amp; Vacances steps in — a trusted name in holiday accommodation for over 50 years.', 'travel, France, destinations, Paris, Azur, Nice, Marseille, Lyon, Bordeaux, Strasbourg, Lille, Normandy, Mont Saint-Michel, Château de Chambord, Château de Chenonceau, Alpes Françaises, Chamonix, Aiguille du Midi, nature, culture, vineyards, h', 12, '2025-06-16 11:25:24', '2025-07-13 13:13:20'),
(8, 'Découvrez Pierre et Vacances: Des Vacances de Rêve à la Française', 'dcouvrez-pierre-et-vacances-des-vacances-de-rve-la-franaise', '<p>Envie de vacances en famille, d’un séjour romantique ou d’un week-end nature ? <strong>Pierre &amp; Vacances</strong> est la référence incontournable pour vivre des expériences uniques dans les plus belles régions de France… et au-delà !</p><p>Depuis plus de 50 ans, <strong>Pierre &amp; Vacances</strong> propose des résidences de tourisme, des appartements tout confort, et des villages vacances pensés pour tous les styles de voyageurs. Que vous cherchiez le calme de la montagne, la douceur du littoral atlantique ou le charme du Sud, vous trouverez forcément la destination parfaite.</p><p><br></p><h3>🏖️ Pourquoi choisir Pierre &amp; Vacances ?</h3><p>✅ <strong>Large choix de destinations</strong> : France, Espagne, Italie, Portugal et plus encore.</p><p> ✅ <strong>Hébergements spacieux et bien équipés</strong> : comme à la maison, mais en mieux !</p><p> ✅ <strong>Activités pour petits et grands</strong> : animations, clubs enfants, piscines, nature...</p><p> ✅ <strong>Flexibilité et liberté</strong> : vous choisissez votre rythme, vos envies, votre style.</p><p> ✅ <strong>Offres spéciales et bons plans</strong> : tout au long de l’année, profitez de réductions exclusives !</p><h3>💚 Des vacances responsables</h3><p>Pierre &amp; Vacances s’engage aussi pour un tourisme plus durable. Leur démarche environnementale (label Green Key, gestion éco-responsable des sites, etc.) vous permet de voyager en toute conscience.</p><p><br></p><h3><img src=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBxMSEhUSEhIVFRUXFxcVFhUVGBYVFRUVFxgXFhUVFhUYHSggGBolGxcVITEhJSorLi4uFx8zODMtNygtLisBCgoKDg0OFxAQGCsdHx0tLS0tLS0tLS0rLS0tLS0tLS0tKy0tKy0rLS0tLS0tLS0tLSstLSstNy0tLSstKy0tLf/AABEIAKoBKAMBIgACEQEDEQH/xAAcAAACAgMBAQAAAAAAAAAAAAAEBQMGAAECBwj/xABIEAABAwEEBAsEBggFBQEAAAABAAIRAwQSITEFQVFxEyIyYXKBkaGxwdEGI7LwM0JSc4LCFCRiY4OSouEVNHTD0kNTo7PxFv/EABkBAAMBAQEAAAAAAAAAAAAAAAABAgMEBf/EACMRAQEAAgMBAAIBBQAAAAAAAAABAhEDEiExQVETBBQiYoH/2gAMAwEAAhEDEQA/APTNFf8AU3DwehXDCp92fAeiJ0Tk/cPB6HnF/wB274SmB1NsUv4zfABMQ3EdHzahXMhgH74I6MRuP5U4kj0f9JW6Lj3/AN0TbKDbr8ByGeLkNYPpavQf8QTC2DiP6DPFyNHtRSzjDcUTZBxRuHktVG8bqPku7MMBu9Fm0TtXS0F0gmLAFtbhAYsWQtoDS2shbhAaWLcLcJG5WSuoWiEaDm8svrTgonBLR7TX1nCoN7yFxwqNmP4ZcutCEDlsJbPSV9pKQ6a0/wAFxdZ1DVgSJ7E5LVRPbVvvdzJP8riPBTbVYyUNU0w6o+695JvOEHLAkZZakLoisXuxnk0zjtLXSO6etbbRAJMYkkz0iT+ZSWPBz+Y0x/4x6qNq0tmi6QaSWhMQSe9A2R8D+buKns1a++6M4cewgHvK1YpHfPeh31xh1+SG4V76jabWwXNvAnDDE+S60foipUFKo5wukvvATMHitgjXIKNh060iTuPwrmhagXAbW3uq6TPcrEdAUnvFS6ZEzBwIu3ACN2yF3X0WynQeWsaC1tyfrQ0wBJxhGwrxc5rnYZA9zgPNYj9KMgu+7n+pixUS16Jyfu/K9DM5R6D/AIHorRfJd8/UehafL/C4f0PWrM6qjij74eKKPKG53koK2X8Vvi1TnlN3O8kQiKw/TvHNUHeE0to4j+i3xKV2X/Mu31B4+ia27kVOiPNMKbUGI6J8Au7OMFlQfAfALugMFnkuOwFsBdALcKVOVtbhbhAcrIXULilVDpjUS08xCA6hbhdQsTJqFuFuFtAahaIXQWEICIhRvCnIUbgkYOq1QFqLqhQEKaqMptUwatMCkASNwQqH7a/SP6HhTcfNegELz72x+kqbnf8AqU5KxLah8vALuxNkPP7VLvphc1BjHOzxaEZoWnLHc9SgO2m1Stf6GhIzEiKrSOm8Ob3BT2LQIpkP1kvGWfCuDx2BoHWVabPREuw+uR2StWxuFMfts+FdHRzXIu/wJt7hC0XxxQ7WBsU1ewNaxxEYFmW8eqbuGB3+SHtjeI78Pi1PpC7VlKzNEiJxGfOlekf8raOk/wCIJ2wcY/h80kt3+WtPSqeIVaidqtpfP+DP9bfRYs0tyj/p/wDcCxRpe1r0byHdIj/xvQdLlt3Ef0uRlgwpnpn/ANbvVB0uW3f5Fasz2vyXfes/IiDyhuPkoLTyX81Rn+2iTyh1oCv0D+tfiqeDk3t/IqdEeaUU/wDN/jf3h3qm+kORU6A80BUamX8M/CF1Z+SFzV5P8P8AIurHyRuWedaYxOAuoWwF0GqNq04AW4Xd1ZCNgm07bKlMBrKd68OVegCM2nEFsiYcMjCBsNjfSqBxdfE3WOL7xc15BvPkTfui7OKf2uyNqNLSXNvAAuYS10DVeGIVftNpbfuvovNNpLRwTrzXzGLmiCSMZnvU2nDDStsrU3tLGA0wHXhOLpEtI2QRjzHmTJtZpbevC6cnThiYHfgldSzNqEAtD2tN5oOL2nKWEmS3DfvEAc2+geB4INc9pwuxcJGOBvYEboKcpaGM0kOFNItc05NJEAwHF3Zd67zUeqjZ7GW8eoDgWOulzmmGOBEy4tLpAxwnCTmrBZtICpjTF5uuCJGzXzJy/srByxI9KaTeyo1rWugcZwuzeEFoaDlynNJOoNxzRdntLuBDpLnxJDi29JOXEMQMMswnsh5UbggLTWrCiwge8vMv8mAJ45icsIzkA8y60bbzUvNc264YxM8XIEkYAzeEcyWz0mqhQwiKgUQCVVHTApAFpoXYCWz04cvPPagzXcNpcO5w8l6K8LzXTzptB6R/OoyrTGBjyjvZ8bU19mGS3+LZ/galLeW78Px/2Tv2QHIG2tZvgCUF+PZLN9b7x35lq1jGl0x4BbsZwPTd5rLXyqXSHgF2OQQ4eP5VFax7t270Ur/PyUVqPu37vRASN5R/D5pHax+r2np1PEJ43lHcPNJLV9Baem/xCZKppPln/T/7gWLNIct3+nPxhYpprVZsKJP7w/DHmg6Z47d/qjqTf1ef2/QICly2bwqI+tZ4tbpMPcz0RkcbtQVt5Nfc09gHojtaQV0mLV/E8U00i73dToDzSut/mf4g8kxt593U6PqgKxV5B+7/ACLuwtwG5Rvyd90PhcibG3LcsOW6ro454LDFu4iKdNScEs5kvQO4tXUW5i4cxOZJ0GupbbdDUqhLi2HFpZeBIN05gbE2fgoCVc9TfCunYvetLmAhjZa+TeDpOG4tcR+HHNd1tHF1Vr711jSH3W4F1TjA3zrbF3DfkmBWKpjEWgdK2cvZAE4zAJaSNgIQuhtFgNFSo0B7mgQMLjIBbTMZluIlOFkp6LYSrYmwTdvHNonGQMLp1Hn50hsrH1qrHcoB0P42DSGw4lrbt4XhGvlA5K0qKjZmMLnNaAXQXRrIAaDG4BK4nKWvcOG4EiNY5LQQILb7cC5pMgRrG9ANtHBFoljn3nNyL6twzxS4C8WhzXG9GN0CAcVYn2RjntqFovtmHa4IIidY4xwUdDR12o54e6HY3MLoJicI5gcNZdtUXxpPWqjVGGox9NcNpKLkqRw1i6uohtJbNNT2VoK4LyzSzprnf5VF6xUbC8i0g6a+9w7xUS3un+GqXLf8/XefJWH2KHGp/fWbwAVboHjP6/8AfPkrN7Ecul99Q8FpinL49asB4v4vIrq2cql0h5KOwHi9fkpLYeNT3/8AFdTlEu8/JR2r6N+70UjvPyUdq5D93kEB23lHc3zSS0/RWjpv8QnVPlHcPNJbUPdV/vHeIQSo6RdFVw1cAR/WFi3pH6R33B+MLEqcXJrf1Wf2vzQljBx2dIJePaQ3zZbogY3tf28o51Ky28YHYQexXolmtQ4tfo/lKNpVJjcqbbPaZzCGloPDcQnKMmzH4u5F0dOlupuGGvV1pdaNibQItP8AEb4hMdIniVOj6qvP0hL+Ew5Qd2EHyWaX9p2saQ5vLF3DVz586NDYVzsHfcj4XIO26TNObuJF0donyW6to4rj+4B/ocq5pq2AXjP12j+ly5eaX2x1cNn5Waze0lT7PeiP/wBM77HeqxorSbDgTe3Zo+ta6QMw7PKF595uSX47v4eO/k1b7TEnFp7lK72npjBxg7j5BU+0W155FN8TndcT4JdaKpmHAg5wQQe9dHHnnfsY8nHhPlei0tKMeLwdI24rv9MbtVV0JW9yN7vFFG1t2reZuW4rCLS3aF0K7doSoWZxxbDhnsW/0Sp9nvHqtJlf0jRrwo2rL6VCyVPs94UtCwPJ40NG+Snu/otGN5dByrlktjXm6HwdhlHcBU1Y7nI7bGjdrkVREqvCz1tsb3eiKFItALnOOMGCQFGd820x+6On0VHwSRXnucQ2o6A5widQOCU6X0xUoYCpLthxjeuH+bG5dY7Jw5a2u7QFzWIASbROkL9NjjmWgneQi31gtIzqO11MF5FaT78b2/DVXqdsq4HcV5TXPv8Arb4P9VpjEWubNU5f4u4V/VW32F+kpfe0u4H0VMsp4rz0/B3/ACVv9jHRUpfeM+Fy1n1GV8er2E8Tr8lPa+VT+diU2W3tbTAJhT1dL0nkFrwQ3B0TgRiQuqxy7OH/AD2Li08h+7yS5+naMcrfhlgVCfaWzvYbriQ4Q0xEnk6ztS0DinyjuHmkts+jr9M+Slbp2mCSQ7Hd6pPatMNc2oxoJvuJBwiMPRPQJLW+ah+4PxLSgtT/AHpGyifGViKIEpWV/wCll8GMcfwR4pq2Vb7RRiz7MB8STUKAvsw+u3xCndGlVt9Co40jdPFcScMhLD5FG1GVDMNccTkDkr3So8dwOPEaAeaX4d/cudGtPBsJJi4GgCRqIE7VP8l18PqptEOuwQZg70o0jYqz2N4jyQTqKuOkaUPcefyCetpAvOCfbItPPq4IpuBGIogHfdIIVU09ix33g8HhXvSdKXV+v8youn/onn99/wA1nm2wFewdKXVXczB23j5K4VaEAfPnuVb9g8KT3bXx1Na31Ks1e0DaP75Lkz3p0TW0tCzCI+daq3ttTipTdtZH8rifzK10KoVY9vDLKbtjy3tE/kT49lfqHQx9x1u8VloyUGg6nuR0neKnrJ2jR/oOvNJnMLv8uHkitLWlwovNM3XtF4c8YkdkpP7OVOI4fZeewgHxlNLdlgtuPLxlniBs+nqjcHwefzKa0dNyMG96qdRsYDANwG76s/hu9qM0e/tW2NYXwXZdBs4ThOMSSTBMNAJkiAmdkqXDBc3MtwzkGDIWWWrChq29oqxeAy7fmE7NQTLd9Nv0lgzcFUrJpSqHPoveXta4tBdDibpwM54iE3t+lqQ929pJcMCLgcNhF4znOeGBVfs+jH8aq6owMLsCb4JwAyDT3E61Nu1as+J2aOPCcKKr+UXXJ4pJ1EawrBobR7HXuEpsccDLmM15jLcoLDY3ReBa9u1t7DfICNt1qNCg+q1l4tg3ZiRIBMwcpnqUZcWPW2RWPJncpLQ2kaApuaGgNERDRA1aguKblCy1utDWvu3ZAMTOfPARDaRXLMvXV1ukNsdxHdE+C8zqY1z85Ar0nSAhj+i7wK80B9+/mveA9Vrj6mxxYx7p5/ZqH+mn6q2+ybCalMfvB3NcqpY2/q5P7px7qY8ldvYyl76n03fC70Vz6zvxZqljc5ocAcPOPRc2LRT6V9rs3Oc8c4cBHgrJo+z3qZG5SWunxx0W+a6N1z6iov0FUAcSDxgYk5SCPNA2T2YrNYwGZY4ExiMHXl6LWaIG4LYEXkehWH2J7m8koCzaPe2cJumCd+IVtpZBCinjV6TPBTbT0p2kbKRVc866ZHYFicabpiD0X+axP0bWO3f5cjd4pJSPHZ02/EE3t7/dHL5KTN5TekPFXIi1ZaR45wHJ8ziVDYCeCbiMsteGv52LdF0OM7PNB2N/FiJInXjmp0ewmlTL3dndCcP2YJLbsSU0bUvAHXAy1p6Ct1aV6pWHOfNUzSGjeEpRtqA9zyr7ZR72rv8AVVrS7wKQIw44+F65P6jOz46/6fCWzbnQWiadNobffGd0OLWyc8k6tdCncIa3jQYJJMGMDiVXLDbcAiK9sBi8cLwOcSZgAnVivImWdy9r0ssMdeRBarBLGzUe18NvQ90EkAkxPPPWq3b7E8TLnOHOS7uKeWq2g5CIgRM5AZu+sdpQdWtIW2GWctTcMbJ4m0FS9yOk7xRNamjNCUA6iIzvO8UZX0NWOVM9ZA81tOTdc+WGi72bPvKjNZAI6pB8QrBVp9gBJSaxaFrU6zahAAEyATOIIwwjMjNPzNzkmdm3FdHHlWOcVF7XXrsSSTjkAMXXid0t/C3DFdWVwBg485u93FHij6mjqxqFwpmC104tzkRmd/ahHaGtX1aQHOXsjucunFyZia1ugZhV616SuVA/Agm647AcJ3eqZn2Ztjv+0N7neTSo6nsRaniDUo454v8A+Ku7qcYJpWZtV968b0ADWBiLr9uUgp3o+zQxzKgmTm3EHY7fGGOcDnKXWH2btVFjINJ7m4ctwBbOAxbnEdYVkpWF8iSBnOJ2cwUSXa6WVLHcdfYei4ECMYdN0bXZ8/NBa2hnCUHgQS6mRqAJc0wY1ZjvUpsb3BzTdAIImTOIiRxcCuqVkqNiHNAGEQTMCBjKvXmkz7sFoLR4FCnMg3G4HAjBMKliEYFFtldhy5f7P/Z1f3N/Spadp3ab+i7wXlF6H13bGvPdTX0BaKDKgh7GuGUOxwSOt7FWB179XAvgh119RsgxOTuYdi0w4Lj+U5c0v4eSsws7hspkdrnD8qvfsRBqt6b/AIHJvX9gbG5paOFYCI4r5wkn64OslMNC+y1OzPDmVKjoLnQ+6eUIzACucd3tFzlh5oww09S6rGanUPBaoMDGxM5Y5ZKCpaWXpvCObHVzLVkYVBl1LCeUgqmkWHbq1D1XZ0hTIPGznMI0HTBgoC3jvHOz4QtNtLY5QWm1RwjsR9X4QFIKNOswO53iViJ00yZ3O8Stq5PE2i7bPBndPelP1hvHimtrPuyNn9kpnEb0yWGmQHbcN+sbEJZog7Zd1YqW8LxIUNmrAEiONfdmJGeA35qTCW04/OoIum/iN6IHcg7acSp6HIG5URdQfD6x3nuKqGnKn6u3pj4XKz3sbR0XfCVUdNu/VhzPb8JXHz4+u3guiyzWjAYoinbBInKRPaMMEls73O5InHm80wslnq4PLRcvQQXCeLdcdeGBC4v4/Xbc/GWy2ScIgQMARqE55nnUDbROAkk5AYoXSdra6oQHBsAGGte4wQDqGJMgwEb7O22z0y91XhJiGl4NJoBzLScS7CNx1zhU491N5NQ80FpY03U6RYQS/N0jPEYEL0KhbQQvI9JadoNumk8Pc17Xi9xWgDNt4Akk4aowVq9nfaijaGk/RwQOM4XXEzAaTEnDYMxtSy4+vxEy7T1fDUB2IKswIdlWDnq8V26sujhY8jHYCVy2da4q1JgbXDuxPgppXVHPXTSpA5Qrlz1SRYeug9A8Itionsh4eug9Aiquatta3PPUNf8A8RsaMryy+lX6fOuN3quuFB/ul3h9TA2hv2h1Y+C5dam6gTuw8Sgr7VzfCOw6inW0/Z7TPkuTaSdZHVCDfXGolQvrlHZOhrmz9btJ81o0+cdqWutBgmYGsnAdqU2n2kotN0PNV2yiC7tdkE9werMWgbFDVrQCTdAGZOAG9xwCq/6fa6uFOk2iPtVHF7uoZTvC7boieNaKz6zs4cTdG5uQS7CSjrR7Q05u02OrO/djiddQ4dgKhNvtTv8AtURuNV3fDe5dSGiGgAcygfUS3VdY6qWmuIP6Q5xGotYGnmLWgYdaxDuKxGxcY9Bt/IOeIHNsSYlNbc4lpn11pO4raMjxr5ccY8hOpCcJD3AgTeOOPyVO2MfXyQ4cwPdeGwjXqx2YpUOLS3b/APedE0CODaN89pQtedfzsUtA8QdfiUAmvY2joP8ABU3TFT9UJ/aZ5q2h2NbnY/wVI0w/9Td02eJHmseSOjiy0E9n8aYn7TviKbWy2spBrX02VCbxF4QYF0Ol107W96pVltr6LpYcNbTyT6HnTi2aWp1+DLcC0PDmnNpPBxlmMDiO5YdI27VLb7W2oLoo02NwwAJMgRN6Rz9qS/4dSBxBJJgY5koo1dgPZ6oevaYguc1kGQS7GebJExk+C3f1PTsrG5U2g7lJUdgckvZbw48V7nmcYH5neqysJN3E7ZJMI1sbkeh6B08XUy97i4Q2CQA4DHlXcDqyGCd2fSLX4tcCNoxVG0IS3igxgMk7pUxMkY/aEh3WRn1pY/43RW9vVobVxbjr8WkeaK4VVyk57eRUE7Kg7r7dXUif8QLfpGED7TeOzuxG8gLaZxncadcKt30qp2xrsWuBG0YqZtZaI0OlYQh21VnDIJO4qt6Vqk1jzBvhPmnr6mCrFsfNepzQOxoCWQSNtThk4qelpNwz9Evc5Yz5+f7LMbOWaWBmcAO/+yhdpuTAII8UprYNO8nu8UryThXJcKdtvZFcWq3lkNaA5xya06ts6hzqlm1OEwT86la7DZwwftGLx1k/OpUMfWVLCauNdxd+wMGDmjX1oulRawQ1oG4LAtXkKduqKJzlpzlEXIDbnKGo9bc5D16oaJJhBuy9Yklpt5dg3Lbr/ssS2HrukKstPNr2pMU3tjRcJx35JMV0RgdU3QcUBbX8cnmHPqjyRzjn3oG2MJcInL1QTZyUlA8Tt8VG1vFXTMGdqARUzxqnRf8ACVSdJGbLUHOzuqNVxov4ztzh3EKi6QtHuqzeY9zgVnn8a4VWaqDuuLuLgc5yugZuJ1IgkucGtEk/Mk6go7S6625TxnlPyvEa+iNQ61i3on/FGH3VWYyv5dZjk9XWgLRoVzSOD47TGOGE6zGY5whqVjc5wYOM5xgNGZKfVWsslO5N98mcTBdrDdjRt1lHz4X36CtVobZWhrILzifMnyUuirdTqECYdsOZ3HWhLXZmV5e0w/WDr37N4SepZy0wRBVTSct/8eo2Qw4J9RXk2i9P1aMB3vGjU48Ybnesr0HQen6NfBjodrY7B46te8LLPFWOSxBbDiMlEx6klSpFWY0mSId9pvFd1kZrmm+q3J4eNjuK7tGB7ApHBQuarl0mi6elGjB8sP7WA/my70Qa+wpUXneOdRBgHJJZ0cv5Th3K5knR8ysq7Xq+9qc7ii6Fqe08aHN2twIy+qfXqQL6rS9xI+s496LkXV256lp5rg0wYgrpjCNSRWMr8k9fp8+aU1sE0tDuKevxSiqU4zyDWYy8dJvxBXqkMFR9HH3jek34grzTOAVKxY5ROeu6rkHUqoWkc9DvqqC02sNEkpNadIudgMB3n0St0IYWvSwbg0S7uG9KatRzzLjPgNwUbApQotVpoLFtxAEnADM7FiA9itTpadyWRgiA4w/FQLss05ZTSpXugmcBty70vqWxtWXB0wcetZpw+6clOgGgMdAGrzT1CPWHiqr6S0sW2xlC86DdN0CRjJxOrJWJ5wVR04PevOu63HXyQlbpUmx9lGJccsTPMvMbY+o8uF4NaZBAGJE5EnHqwVl0ZXc5kuc4naSSkLtZ51jyXyNeOF36KQCGmL2ZjEjZJ1cygdYpwguOUbdggJu1H6EaOGGGTXEcxAwI51k1AU7M2yNIiazhDrv/AEwf+m0j6x1nUk9ay3iX1H9TRkNgJRFZ5zkyRJ37UPZRIk4nac1OzrltFg5LSTtk9xwCyvQLhiB4n+yOYpHhLZ6IKlj5lA6zOGInDEEYEHaFYXjBcBojJV2qesd6H9sq1KG1hwrduAqDryd1wedXnROm6NoE0ngkZtODm72nFeX29onJAsqOY4OaS1wIgtJBGOohPUpb09vvLkoWyOJa2TqHgiQoUjcFG5qmK4KCQEQg6zYeekUe5CWrl9Z8kHildzIuhVO9COy6/NF08kHpxarpacI3b0rq2bAwdXzimVbkHr8UrYcR86lUyZZ4wLZaZDxIwlvxBXBlXBVknwHkfFOaB4quJxjq3WoNzSK06WnBgnnOX91zp4zUYNUHDV2JeErVtuJcZJkrAtrh6hUSByM0bYald9yk2ThJya0HW46h3nVKXhemeyDALLSgAXgS6PrGYk7TAHYrwx7VOeXWPFrPa6lpt1Ozu4rRWDDTH7LodeP1jgT1LE0t7QLbIz4QY689qxayRm//2Q==\" alt=\"Das sind die besten Pierre &amp; Vacances ...\"></h3><h3><br></h3><h3>📅 Réservez dès maintenant !</h3><p>Que ce soit pour une escapade de quelques jours ou un long séjour en famille, <strong>Pierre &amp; Vacances</strong> vous offre le confort, la liberté et les services pour des vacances réussies. Et grâce à <strong>Tripnest.fr</strong>, profitez de <strong>réductions exclusives</strong> et de <strong>codes promo</strong> pour faire rimer plaisir avec économies !</p><p>👉 Découvrez toutes les offres Pierre &amp; Vacances ici sur Tripnest.fr.</p>', 'Profitez de vacances inoubliables avec Pierre &amp; Vacances : hébergements confortables, destinations magnifiques, et offres exclusives à découvrir sur Tripnest.fr !', '6856fd06dbb62.png', 5, 1, 'published', '🌞 Découvrez Pierre &amp; Vacances: Des Vacances de Rêve à la Française', 'Réservez vos vacances avec Pierre &amp; Vacances : résidences confortables, destinations ensoleillées et bons plans exclusifs. Découvrez toutes les offres sur Tripnest.fr !', 'travel, France, destinations, Paris, Nice, Marseille, Lyon, Bordeaux, Strasbourg, Lille, Normandy, Mont Saint-Michel, Château de Chambord, Château de Chenonceau, Alpes Françaises, Chamonix, Aiguille du Midi, nature, culture, vineyards, h', 7, '2025-06-21 18:42:14', '2025-07-12 12:56:15');

-- --------------------------------------------------------

--
-- Table structure for table `post_tags`
--

CREATE TABLE `post_tags` (
  `post_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'site_title', 'TripNest', '2025-03-24 16:08:39', '2025-03-24 22:52:38'),
(2, 'site_description', 'Your Ultimate Travel Guide', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(3, 'site_email', '<EMAIL>', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(4, 'posts_per_page', '10', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(5, 'comments_per_page', '5', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(6, 'allow_comments', '1', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(7, 'require_comment_approval', '1', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(8, 'date_format', 'd/m/Y', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(9, 'time_format', 'H:i', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(10, 'currency', 'EUR', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(11, 'currency_symbol', '€', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(12, 'google_analytics', '', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(13, 'facebook_pixel', '', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(14, 'recaptcha_site_key', '', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(15, 'recaptcha_secret_key', '', '2025-03-24 16:08:39', '2025-03-24 16:08:39'),
(49, 'site_logo', '', '2025-04-30 23:41:29', '2025-04-30 23:41:29'),
(50, 'footer_logo', '', '2025-04-30 23:41:29', '2025-04-30 23:41:29'),
(51, 'site_name', 'TRIP NEST', '2025-04-30 23:41:29', '2025-04-30 23:41:29'),
(52, 'social_facebook', '', '2025-04-30 23:41:29', '2025-04-30 23:41:29'),
(53, 'social_instagram', '', '2025-04-30 23:41:29', '2025-05-10 18:34:45'),
(54, 'social_snapchat', '', '2025-04-30 23:41:29', '2025-04-30 23:41:29'),
(55, 'social_youtube', '', '2025-04-30 23:41:29', '2025-05-10 18:34:45'),
(56, 'social_email', '', '2025-04-30 23:41:29', '2025-04-30 23:53:55'),
(57, 'social_tiktok', '', '2025-04-30 23:41:29', '2025-05-10 18:34:45'),
(58, 'social_telegram', '', '2025-04-30 23:41:29', '2025-04-30 23:41:29'),
(59, 'social_pinterest', '', '2025-04-30 23:41:29', '2025-05-10 18:34:45'),
(60, 'footer_copyright', '© 2025 Tripnest.fr – Tous droits réservés. Géré par Brosoltech Ltd', '2025-04-30 23:41:29', '2025-05-10 18:37:21'),
(61, 'footer_credit', '© 2025 Tripnest.fr', '2025-04-30 23:41:29', '2025-06-02 15:36:49'),
(88, 'header_html', '', '2025-05-01 00:07:26', '2025-05-01 00:07:26'),
(89, 'footer_html', '', '2025-05-01 00:07:26', '2025-05-01 00:07:26'),
(103, 'country_link_1', 'tripnest.fr', '2025-05-04 21:34:34', '2025-05-10 18:36:59'),
(104, 'country_link_1_url', 'https://tripnest.fr/', '2025-05-04 21:34:34', '2025-05-10 18:36:59'),
(105, 'country_link_2', 'tripnest.fr', '2025-05-04 21:34:34', '2025-05-10 18:36:59'),
(106, 'country_link_2_url', 'https://tripnest.fr/', '2025-05-04 21:34:34', '2025-05-10 18:36:59'),
(107, 'show_coupon_header', '0', '2025-05-04 21:34:34', '2025-05-08 09:40:56'),
(108, 'show_search_coupons', '1', '2025-05-04 21:34:34', '2025-05-04 21:34:34');

-- --------------------------------------------------------

--
-- Table structure for table `stores`
--

CREATE TABLE `stores` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `website_url` varchar(255) DEFAULT NULL,
  `affiliate_id` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `stores`
--

INSERT INTO `stores` (`id`, `name`, `slug`, `description`, `logo`, `website_url`, `affiliate_id`, `status`, `created_at`, `updated_at`) VALUES
(4, 'Sunparks', 'sunparks', 'Profitez d\'escapades en famille chez Sunparks, qui propose des parcs de vacances relaxants en France', '6808cd3038f59.png', 'https://www.jdoqocy.com/click-101436905-12470051', '786', 'active', '2025-04-23 11:21:20', '2025-05-11 11:57:31'),
(5, 'Pierre & Vacances', 'pierre-vacances', 'Découvrez les meilleures locations de vacances en France avec Pierre & Vacances', '680f1aa6cf026.png', 'https://www.tkqlhce.com/click-101436905-10665515', '', 'active', '2025-04-28 06:05:26', '2025-05-11 11:58:19');

-- --------------------------------------------------------

--
-- Table structure for table `tags`
--

CREATE TABLE `tags` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `slug` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','editor','user') NOT NULL DEFAULT 'user',
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password`, `role`, `first_name`, `last_name`, `avatar`, `created_at`, `updated_at`) VALUES
(1, 'Arshad', '<EMAIL>', '$2y$10$A5YbGMi9BRG84EphqbILKOzEkGNc1EKhkjAbWo12qIXZQmgrcyeza', 'admin', 'Arshad', 'Mehmood', NULL, '2025-03-24 16:08:39', '2025-05-10 18:39:05');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `comments`
--
ALTER TABLE `comments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `post_id` (`post_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `coupons`
--
ALTER TABLE `coupons`
  ADD PRIMARY KEY (`id`),
  ADD KEY `store_id` (`store_id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `coupon_id` (`coupon_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `pages`
--
ALTER TABLE `pages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `posts`
--
ALTER TABLE `posts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `author_id` (`author_id`);

--
-- Indexes for table `post_tags`
--
ALTER TABLE `post_tags`
  ADD PRIMARY KEY (`post_id`,`tag_id`),
  ADD KEY `tag_id` (`tag_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `stores`
--
ALTER TABLE `stores`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `tags`
--
ALTER TABLE `tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `comments`
--
ALTER TABLE `comments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `coupons`
--
ALTER TABLE `coupons`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `pages`
--
ALTER TABLE `pages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `posts`
--
ALTER TABLE `posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=337;

--
-- AUTO_INCREMENT for table `stores`
--
ALTER TABLE `stores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `tags`
--
ALTER TABLE `tags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `comments`
--
ALTER TABLE `comments`
  ADD CONSTRAINT `comments_ibfk_1` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `comments_ibfk_3` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `coupons`
--
ALTER TABLE `coupons`
  ADD CONSTRAINT `coupons_ibfk_1` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `coupons_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  ADD CONSTRAINT `coupon_usage_ibfk_1` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `coupon_usage_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `posts`
--
ALTER TABLE `posts`
  ADD CONSTRAINT `posts_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `posts_ibfk_2` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
