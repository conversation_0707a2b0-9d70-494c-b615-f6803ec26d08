<?php
require_once 'includes/config.php';
require_once 'includes/db_queries.php';

// Get coupon ID from URL
$coupon_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get coupon details
$coupon = getCouponDetails($coupon_id);

// If coupon not found or expired, redirect to coupons page
if (!$coupon) {
    header('Location: coupons.php');
    exit;
}

// Handle coupon code reveal and affiliate link
if (isset($_POST['reveal_code'])) {
    // Track coupon usage
    $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
    trackCouponUsage($coupon_id, $user_id);

    // Return JSON response for AJAX request
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        echo json_encode([
            'code' => $coupon['code'],
            'affiliate_link' => $coupon['affiliate_link']
        ]);
        exit;
    }

    // Fallback for non-AJAX requests
    header('Location: coupon.php?id=' . $coupon_id . '&code_revealed=1');
    exit;
}

include 'includes/header.php';
?>

<div class="container">
    <div class="main-content">
        <div class="content-area">
            <div class="coupon-detail">
                <!-- Store Header Section -->
                <div class="store-header">
                    <div class="store-info">
                        <?php if (!empty($coupon['store_logo'])): ?>
                        <img src="uploads/stores/<?php echo htmlspecialchars($coupon['store_logo']); ?>"
                             alt="<?php echo htmlspecialchars($coupon['store_name']); ?>"
                             class="store-logo">
                        <?php endif; ?>
                        <div class="store-text">
                            <h1><?php echo htmlspecialchars($coupon['store_name']); ?></h1>
                            <?php if (!empty($coupon['store_url'])): ?>
                            <a href="<?php echo htmlspecialchars($coupon['store_url']); ?>" target="_blank" class="store-link">
                                <i class="fas fa-external-link-alt"></i> Visiter le site
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Coupon Content Section -->
                <div class="coupon-content">
                    <div class="discount-badge <?php echo $coupon['discount_type'] === 'percentage' ? 'percentage' : 'fixed'; ?>">
                        <span class="discount-value">
                            <?php if ($coupon['discount_type'] === 'percentage'): ?>
                                -<?php echo $coupon['discount_value']; ?>%
                            <?php else: ?>
                                -<?php echo $coupon['discount_value']; ?>€
                            <?php endif; ?>
                        </span>
                        <span class="discount-label">de réduction</span>
                    </div>

                    <h2 class="coupon-title">
                        <?php
                        // Generate dynamic title if description is empty or generic
                        if (empty(trim($coupon['description'])) || strtolower(trim($coupon['description'])) == 'coupon code' || strtolower(trim($coupon['description'])) == 'code promo') {
                            if ($coupon['discount_type'] === 'percentage') {
                                echo htmlspecialchars($coupon['discount_value'] . '% de réduction chez ' . $coupon['store_name']);
                            } else {
                                echo htmlspecialchars($coupon['discount_value'] . '€ de réduction chez ' . $coupon['store_name']);
                            }
                        } else {
                            echo htmlspecialchars($coupon['description']);
                        }
                        ?>
                    </h2>

                    <!-- Coupon Details Grid -->
                    <div class="coupon-details-grid">
                        <div class="detail-item">
                            <i class="far fa-clock"></i>
                            <div class="detail-content">
                                <span class="detail-label">Expire le</span>
                                <span class="detail-value"><?php echo date('d/m/Y', strtotime($coupon['end_date'])); ?></span>
                            </div>
                        </div>

                        <?php if ($coupon['minimum_purchase']): ?>
                        <div class="detail-item">
                            <i class="fas fa-shopping-cart"></i>
                            <div class="detail-content">
                                <span class="detail-label">Minimum d'achat</span>
                                <span class="detail-value"><?php echo $coupon['minimum_purchase']; ?>€</span>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($coupon['usage_limit']): ?>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <div class="detail-content">
                                <span class="detail-label">Utilisations</span>
                                <span class="detail-value"><?php echo $coupon['used_count']; ?>/<?php echo $coupon['usage_limit']; ?></span>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($coupon['category_name']): ?>
                        <div class="detail-item">
                            <i class="fas fa-tag"></i>
                            <div class="detail-content">
                                <span class="detail-label">Catégorie</span>
                                <span class="detail-value"><?php echo htmlspecialchars($coupon['category_name']); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Coupon Code Section -->
                    <div class="coupon-code-section" id="couponCodeSection">
                        <?php if (isset($_GET['code_revealed'])): ?>
                        <div class="coupon-code-revealed">
                            <div class="code-box">
                                <span class="code"><?php echo htmlspecialchars($coupon['code']); ?></span>
                                <button class="copy-btn" onclick="copyCode(this, '<?php echo htmlspecialchars($coupon['affiliate_link']); ?>')" data-tooltip="Copier le code">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <p class="code-instructions">
                                <i class="fas fa-info-circle"></i>
                                Copiez ce code et collez-le lors de votre commande sur
                                <a href="<?php echo htmlspecialchars($coupon['affiliate_link'] ?: $coupon['store_url']); ?>" target="_blank">
                                    <?php echo htmlspecialchars($coupon['store_name']); ?>
                                </a>
                            </p>
                        </div>
                        <?php else: ?>
                        <div class="reveal-section">
                            <button onclick="revealCode()" class="btn-reveal">
                                <i class="fas fa-unlock-alt"></i>
                                Révéler le code promo
                            </button>
                            <p class="reveal-info">
                                <i class="fas fa-shield-alt"></i>
                                Code vérifié <?php echo date('d/m/Y'); ?>
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <?php include 'includes/sidebar.php'; ?>
    </div>
</div>

<style>
.coupon-detail {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

/* Store Header Section */
.store-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-bottom: 1px solid #eee;
}

.store-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.store-logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    background: #fff;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.store-text h1 {
    font-size: 1.75rem;
    color: #2d3436;
    margin: 0 0 0.5rem 0;
}

.store-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #0066cc;
    text-decoration: none;
    font-size: 0.95rem;
    transition: color 0.2s;
}

.store-link:hover {
    color: #0052a3;
}

/* Coupon Content Section */
.coupon-content {
    padding: 2rem;
    text-align: center;
}

.discount-badge {
    display: inline-flex;
    flex-direction: column;
    padding: 1rem 2rem;
    border-radius: 8px;
    color: #fff;
    margin-bottom: 1.5rem;
}

.discount-badge.percentage {
    background: linear-gradient(135deg, #00b894 0%, #00a885 100%);
}

.discount-badge.fixed {
    background: linear-gradient(135deg, #0984e3 0%, #0769b5 100%);
}

.discount-value {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
}

.discount-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 0.25rem;
}

.coupon-title {
    font-size: 1.5rem;
    color: #2d3436;
    margin: 0 0 2rem 0;
    line-height: 1.4;
}

/* Coupon Details Grid */
.coupon-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
    text-align: left;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.detail-item i {
    font-size: 1.25rem;
    color: #0066cc;
    margin-top: 0.25rem;
}

.detail-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-label {
    font-size: 0.85rem;
    color: #666;
}

.detail-value {
    font-size: 1rem;
    color: #2d3436;
    font-weight: 500;
}

/* Coupon Code Section */
.coupon-code-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.coupon-code-revealed {
    animation: fadeIn 0.3s ease;
}

.code-box {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: #f1f2f6;
    padding: 1rem 2rem;
    border-radius: 8px;
    border: 2px dashed #0066cc;
}

.code {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: bold;
    color: #0066cc;
    letter-spacing: 1px;
}

.copy-btn {
    background: #fff;
    border: none;
    color: #666;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.copy-btn:hover {
    background: #0066cc;
    color: #fff;
}

.copy-btn[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: #fff;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    margin-bottom: 0.5rem;
}

.code-instructions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.95rem;
    margin: 1rem 0 0 0;
}

.code-instructions a {
    color: #0066cc;
    text-decoration: none;
}

.code-instructions a:hover {
    text-decoration: underline;
}

.reveal-section {
    text-align: center;
}

.btn-reveal {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: #0066cc;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-reveal:hover {
    background: #0052a3;
    transform: translateY(-2px);
}

.reveal-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
    margin: 1rem 0 0 0;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .store-info {
        flex-direction: column;
        text-align: center;
    }

    .store-logo {
        width: 80px;
        height: 80px;
    }

    .store-text h1 {
        font-size: 1.5rem;
    }

    .coupon-title {
        font-size: 1.25rem;
    }

    .coupon-details-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .code {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .store-header,
    .coupon-content {
        padding: 1.5rem;
    }

    .discount-badge {
        padding: 0.75rem 1.5rem;
    }

    .discount-value {
        font-size: 1.75rem;
    }

    .code-box {
        padding: 0.75rem 1.5rem;
    }

    .btn-reveal {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
async function revealCode() {
    try {
        const response = await fetch('coupon.php?id=<?php echo $coupon_id; ?>', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: 'reveal_code=1'
        });

        const data = await response.json();

        // Update the coupon code section
        const codeSection = document.getElementById('couponCodeSection');
        codeSection.innerHTML = `
            <div class="coupon-code-revealed">
                <div class="code-box">
                    <span class="code">${data.code}</span>
                    <button class="copy-btn" onclick="copyCode(this, '${data.affiliate_link}')" data-tooltip="Copier le code">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <p class="code-instructions">
                    <i class="fas fa-info-circle"></i>
                    Copiez ce code et collez-le lors de votre commande sur
                    <a href="${data.affiliate_link}" target="_blank"><?php echo htmlspecialchars($coupon['store_name']); ?></a>
                </p>
            </div>
        `;

        // Open affiliate link in new tab
        if (data.affiliate_link) {
            window.open(data.affiliate_link, '_blank');
        }
    } catch (error) {
        console.error('Error revealing code:', error);
    }
}

function copyCode(button, affiliateLink) {
    const code = button.parentElement.querySelector('.code').textContent;
    navigator.clipboard.writeText(code).then(() => {
        const icon = button.querySelector('i');
        icon.className = 'fas fa-check';
        button.setAttribute('data-tooltip', 'Copié !');

        // Store the copied code in sessionStorage
        sessionStorage.setItem('copiedCouponCode', code);

        // Open affiliate link in new tab if provided
        if (affiliateLink) {
            window.open(affiliateLink, '_blank');
        }

        setTimeout(() => {
            icon.className = 'fas fa-copy';
            button.setAttribute('data-tooltip', 'Copier le code');
        }, 2000);
    });
}

// Check if there's a copied coupon code in sessionStorage when the page loads
document.addEventListener('DOMContentLoaded', function() {
    const copiedCode = sessionStorage.getItem('copiedCouponCode');
    if (copiedCode && !document.querySelector('.coupon-code-revealed')) {
        // If there's a copied code and the code is not already revealed, show it
        const codeSection = document.getElementById('couponCodeSection');
        if (codeSection && codeSection.querySelector('.reveal-section')) {
            codeSection.innerHTML = `
                <div class="coupon-code-revealed">
                    <div class="code-box">
                        <span class="code">${copiedCode}</span>
                        <button class="copy-btn" onclick="copyCode(this, '<?php echo htmlspecialchars($coupon['affiliate_link']); ?>')" data-tooltip="Copier le code">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <p class="code-instructions">
                        <i class="fas fa-info-circle"></i>
                        Copiez ce code et collez-le lors de votre commande sur
                        <a href="<?php echo htmlspecialchars($coupon['affiliate_link']); ?>" target="_blank"><?php echo htmlspecialchars($coupon['store_name']); ?></a>
                    </p>
                </div>
            `;
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>