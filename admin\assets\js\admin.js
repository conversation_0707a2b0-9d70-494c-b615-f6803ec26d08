// Admin Panel JavaScript

// Document Ready
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Image preview
    $('.image-upload').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('.image-preview').attr('src', e.target.result);
            }
            reader.readAsDataURL(file);
        }
    });

    // Confirm delete
    $('.delete-confirm').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        if (confirm('Are you sure you want to delete this item?')) {
            window.location.href = url;
        }
    });

    // Dynamic form fields
    $('.add-field').on('click', function() {
        var template = $(this).data('template');
        var container = $(this).data('container');
        $(container).append(template);
    });

    // Remove dynamic field
    $(document).on('click', '.remove-field', function() {
        $(this).closest('.dynamic-field').remove();
    });

    // Sortable tables
    if ($('.sortable').length) {
        $('.sortable').sortable({
            handle: '.sort-handle',
            update: function(event, ui) {
                var items = [];
                $('.sortable tr').each(function() {
                    items.push($(this).data('id'));
                });
                $.post('update_order.php', { items: items });
            }
        });
    }

    // AJAX form submission
    $('.ajax-form').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...');
        
        $.ajax({
            url: form.attr('action'),
            type: form.attr('method'),
            data: new FormData(this),
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    if (response.redirect) {
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 1500);
                    }
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                showAlert('danger', 'An error occurred. Please try again.');
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Status toggle
    $('.status-toggle').on('change', function() {
        var id = $(this).data('id');
        var status = $(this).prop('checked') ? 1 : 0;
        var type = $(this).data('type');
        
        $.post('toggle_status.php', {
            id: id,
            status: status,
            type: type
        }, function(response) {
            if (response.success) {
                showAlert('success', 'Status updated successfully');
            } else {
                showAlert('danger', 'Failed to update status');
                $(this).prop('checked', !status);
            }
        });
    });

    // Search functionality
    $('.search-input').on('keyup', function() {
        var searchText = $(this).val().toLowerCase();
        var table = $(this).closest('.card').find('table');
        
        table.find('tbody tr').each(function() {
            var rowText = $(this).text().toLowerCase();
            $(this).toggle(rowText.indexOf(searchText) > -1);
        });
    });

    // Date range picker
    if ($('.date-range').length) {
        $('.date-range').daterangepicker({
            locale: {
                format: 'DD/MM/YYYY'
            }
        });
    }

    // Select2 initialization
    if ($.fn.select2) {
        $('.select2').select2({
            theme: 'bootstrap-5'
        });
    }
});

// Show alert message
function showAlert(type, message) {
    var alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    $('.alert-container').html(alertHtml);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

// Format date
function formatDate(date) {
    var d = new Date(date);
    return d.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR'
    }).format(amount);
}

// Slug generator
function generateSlug(text) {
    return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/\s+/g, '-')
        .replace(/[^\w\-]+/g, '')
        .replace(/\-\-+/g, '-');
}

// Copy to clipboard
function copyToClipboard(text) {
    var textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
    showAlert('success', 'Copied to clipboard!');
}

// File size formatter
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    var k = 1024;
    var sizes = ['Bytes', 'KB', 'MB', 'GB'];
    var i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Character counter
function updateCharCount(element, maxLength) {
    var currentLength = element.val().length;
    var counter = element.siblings('.char-count');
    counter.text(currentLength + '/' + maxLength);
    
    if (currentLength > maxLength) {
        counter.addClass('text-danger');
    } else {
        counter.removeClass('text-danger');
    }
} 