<?php
require_once 'includes/config.php';
require_once 'includes/db_queries.php';

// Support both slug and id for backward compatibility
$store_slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';
$store_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($store_slug) {
    // Fetch store by slug
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM stores WHERE slug = :slug AND status = 'active'");
    $stmt->bindValue(':slug', $store_slug, PDO::PARAM_STR);
    $stmt->execute();
    $store = $stmt->fetch(PDO::FETCH_ASSOC);
} elseif ($store_id) {
    // Fetch store by ID (legacy)
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM stores WHERE id = :id AND status = 'active'");
    $stmt->bindValue(':id', $store_id, PDO::PARAM_INT);
    $stmt->execute();
    $store = $stmt->fetch(PDO::FETCH_ASSOC);
} else {
    header('Location: /coupons');
    exit;
}

if (!$store) {
    include 'includes/header.php';
    echo '<div class="container"><div class="main-content"><div class="content-area">';
    echo '<div class="no-results"><i class="fas fa-store"></i><h3>Boutique introuvable</h3><p>La boutique demandée n\'existe pas ou n\'est pas active.</p><a href="/coupons" class="btn-primary">Retour aux boutiques</a></div>';
    echo '</div></div></div>';
    include 'includes/footer.php';
    exit;
}

// Fetch all active coupons for this store
$coupons = getCouponsByStore($store['id']);

$categories = getAllCategories();
include 'includes/header.php';
?>
<div class="container store-detail-container">
    <div class="main-content">
        <div class="content-area">
            <div class="page-header store-header-hero">
                <div class="store-header-flex">
                    <div class="store-header-logo-wrap">
                        <?php if (!empty($store['logo'])): ?>
                            <img src="/uploads/stores/<?php echo htmlspecialchars($store['logo']); ?>" alt="<?php echo htmlspecialchars($store['name']); ?>" class="store-logo-hero">
                        <?php else: ?>
                            <div class="store-logo-placeholder"><i class="fas fa-store"></i></div>
                        <?php endif; ?>
                    </div>
                    <div class="store-header-info">
                        <h1 class="page-title store-title"> <?php echo htmlspecialchars($store['name']); ?> </h1>
                        <p class="page-description store-desc"> <?php echo htmlspecialchars($store['description'] ?? ''); ?> </p>
                        <div class="store-header-actions">
                            <?php if (!empty($store['website_url'])): ?>
                                <a href="<?php echo htmlspecialchars($store['website_url']); ?>" target="_blank" class="btn-primary store-visit-btn">
                                    <i class="fas fa-external-link-alt"></i> Visiter le site
                                </a>
                            <?php endif; ?>
                            <a href="/coupons" class="btn-secondary store-back-btn"><i class="fas fa-arrow-left"></i> Retour aux boutiques</a>
                        </div>
                    </div>
                </div>
            </div>
            <section class="coupons-list store-coupons-list" id="coupons-section">
                <h2 class="coupons-section-title"><i class="fas fa-ticket-alt"></i> Coupons disponibles</h2>
                <?php if (!empty($coupons)): ?>
                    <div class="coupons-grid">
                    <?php foreach ($coupons as $coupon): ?>
                        <div class="coupon-article coupon-card">
                            <div class="coupon-article-header">
                                <div class="coupon-meta">
                                    <span class="discount-badge <?php echo $coupon['discount_type'] === 'percentage' ? 'percentage' : 'fixed'; ?>">
                                        <?php if ($coupon['discount_type'] === 'percentage'): ?>
                                            <?php echo (int)$coupon['discount_value']; ?>% OFF
                                        <?php else: ?>
                                            <?php echo number_format($coupon['discount_value'], 0, ',', ' '); ?> <?php echo htmlspecialchars($coupon['currency'] ?? ''); ?> OFF
                                        <?php endif; ?>
                                    </span>
                                    <?php if (!empty($coupon['category_name'])): ?>
                                    <span class="category-badge">
                                        <i class="<?php echo !empty($coupon['category_icon']) ? $coupon['category_icon'] : 'fas fa-tag'; ?>"></i>
                                        <?php echo htmlspecialchars($coupon['category_name']); ?>
                                    </span>
                                    <?php endif; ?>
                                    <span class="coupon-date">
                                        <i class="fas fa-calendar-alt"></i>
                                        Valide jusqu'au <?php echo date('d/m/Y', strtotime($coupon['end_date'])); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="coupon-article-content">
                                <h3>
                                    <?php
                                    // Generate dynamic title if description is empty or generic
                                    if (empty(trim($coupon['description'])) || strtolower(trim($coupon['description'])) == 'coupon code' || strtolower(trim($coupon['description'])) == 'code promo') {
                                        if ($coupon['discount_type'] === 'percentage') {
                                            echo htmlspecialchars($coupon['discount_value'] . '% de réduction chez ' . $store['name']);
                                        } else {
                                            echo htmlspecialchars($coupon['discount_value'] . '€ de réduction chez ' . $store['name']);
                                        }
                                    } else {
                                        echo htmlspecialchars($coupon['description']);
                                    }
                                    ?>
                                </h3>
                                <div class="coupon-details">
                                    <div class="coupon-detail"><i class="fas fa-info-circle"></i> <?php echo htmlspecialchars($coupon['description']); ?></div>
                                    <?php if (!empty($coupon['code'])): ?>
                                    <div class="coupon-detail coupon-code-detail">
                                        <i class="fas fa-ticket-alt"></i> <strong>Code :</strong>
                                        <button class="reveal-code-btn" onclick="revealStoreCode(this, '<?php echo htmlspecialchars($coupon['affiliate_link'] ?: $store['website_url']); ?>')">Afficher le code</button>
                                        <span class="coupon-code" style="display:none;"> <?php echo htmlspecialchars($coupon['code']); ?></span>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="coupon-article-footer">
                                <a href="<?php echo !empty($coupon['affiliate_link']) ? htmlspecialchars($coupon['affiliate_link']) : (!empty($store['website_url']) ? htmlspecialchars($store['website_url']) : '#'); ?>" target="_blank" class="btn-primary coupon-use-btn">
                                    <i class="fas fa-shopping-cart"></i> Utiliser l'offre
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="no-results">
                        <i class="fas fa-ticket-alt"></i>
                        <h3>Aucun coupon disponible</h3>
                        <p>Il n'y a actuellement aucun coupon actif pour cette boutique.</p>
                        <a href="/coupons" class="btn-primary">Retour aux boutiques</a>
                    </div>
                <?php endif; ?>
            </section>
        </div>
        <?php include 'includes/sidebar.php'; ?>
    </div>
</div>
<style>
.store-detail-container {
    max-width: 1100px;
    margin: 0 auto;
}
.store-header-hero {
    background: linear-gradient(135deg, #0A0A4A 0%, #1a1a7a 100%);
    color: white;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(10,10,74,0.10);
    padding: 2.5rem 2rem 2rem 2rem;
    margin-bottom: 2.5rem;
}
.store-header-flex {
    display: flex;
    align-items: center;
    gap: 2.5rem;
    flex-wrap: wrap;
}
.store-header-logo-wrap {
    flex-shrink: 0;
}
.store-logo-hero {
    width: 120px;
    height: 120px;
    object-fit: contain;
    background: white;
    padding: 1rem;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.10);
    border: 2px solid #fff;
}
.store-logo-placeholder {
    width: 120px;
    height: 120px;
    background: #f5f5f5;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #bbb;
    border: 2px solid #fff;
}
.store-header-info {
    flex: 1;
    min-width: 220px;
}
.store-title {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
    color: #fff;
}
.store-desc {
    color: rgba(255,255,255,0.92);
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
}
.store-header-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}
.btn-primary.store-visit-btn {
    background: #FF7F00;
    color: #fff;
    font-weight: 600;
    border-radius: 8px;
    padding: 0.7rem 1.5rem;
    font-size: 1rem;
    transition: background 0.2s;
}
.btn-primary.store-visit-btn:hover {
    background: #ff9a36;
    color: #fff;
}
.btn-secondary.store-back-btn {
    background: #fff;
    color: #0A0A4A;
    font-weight: 500;
    border-radius: 8px;
    padding: 0.7rem 1.5rem;
    font-size: 1rem;
    border: 1px solid #0A0A4A;
    text-decoration: none;
    transition: background 0.2s, color 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.btn-secondary.store-back-btn:hover {
    background: #0A0A4A;
    color: #fff;
}
.coupons-section-title {
    font-size: 1.4rem;
    color: #0A0A4A;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.7rem;
}
.coupons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}
.coupon-card {
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(10,10,74,0.07);
    border: 1px solid #eee;
    background: #fff;
    transition: box-shadow 0.2s, border 0.2s;
    display: flex;
    flex-direction: column;
    min-height: 260px;
}
.coupon-card:hover {
    box-shadow: 0 6px 24px rgba(10,10,74,0.13);
    border: 1.5px solid #FF7F00;
}
.coupon-article-header {
    padding: 1.2rem 1.2rem 0.5rem 1.2rem;
    border-bottom: 1px solid #f2f2f2;
}
.coupon-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.7rem 1.2rem;
    align-items: center;
}
.discount-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1.1rem;
    border-radius: 8px;
    font-weight: bold;
    font-size: 1.1rem;
    color: white;
}
.discount-badge.percentage {
    background: #0A0A4A;
}
.discount-badge.fixed {
    background: #FF7F00;
}
.category-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #f5f5f5;
    color: #0A0A4A;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
}
.category-badge i {
    color: #FF7F00;
}
.coupon-date {
    color: #888;
    font-size: 0.97rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.coupon-date i {
    color: #FF7F00;
}
.coupon-article-content {
    flex-grow: 1;
    padding: 1.2rem 1.2rem 0.5rem 1.2rem;
}
.coupon-article-content h3 {
    font-size: 1.18rem;
    color: #0A0A4A;
    margin-bottom: 1rem;
    line-height: 1.4;
}
.coupon-details {
    display: grid;
    gap: 0.7rem;
    margin-bottom: 1.2rem;
}
.coupon-detail {
    display: flex;
    align-items: center;
    gap: 0.7rem;
    color: #555;
    font-size: 0.98rem;
}
.coupon-detail i {
    color: #0A0A4A;
}
.coupon-code-detail {
    background: #f5f5f5;
    border-radius: 6px;
    padding: 0.4rem 0.8rem;
    font-size: 1.08rem;
    font-family: monospace;
    color: #0A0A4A;
    margin-top: 0.3rem;
}
.coupon-code {
    font-weight: bold;
    letter-spacing: 1px;
    color: #FF7F00;
    font-size: 1.1em;
}
.coupon-article-footer {
    margin-top: auto;
    text-align: center;
    padding: 1.2rem;
}
.btn-primary.coupon-use-btn {
    background: #0A0A4A;
    color: #fff;
    border-radius: 8px;
    padding: 0.7rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    transition: background 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.7rem;
    text-decoration: none;
}
.btn-primary.coupon-use-btn:hover {
    background: #FF7F00;
    color: #fff;
}
.no-results {
    text-align: center;
    padding: 3rem 1.5rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(10,10,74,0.07);
    margin-top: 2rem;
}
.no-results i {
    font-size: 2.5rem;
    color: #FF7F00;
    margin-bottom: 1rem;
}
.no-results h3 {
    font-size: 1.3rem;
    color: #0A0A4A;
    margin-bottom: 1rem;
}
.no-results p {
    color: #888;
    margin-bottom: 2rem;
}
.no-results .btn-primary {
    max-width: 220px;
    margin: 0 auto;
}
.reveal-code-btn {
    background: #FF7F00;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 0.3rem 1rem;
    font-size: 1.05rem;
    font-weight: 600;
    cursor: pointer;
    margin-left: 0.5rem;
    transition: background 0.2s;
}
.reveal-code-btn:hover {
    background: #0A0A4A;
}
.coupon-code {
    font-weight: bold;
    letter-spacing: 1px;
    color: #FF7F00;
    font-size: 1.1em;
    margin-left: 0.5rem;
}
@media (max-width: 900px) {
    .store-header-flex {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
    }
    .store-header-logo-wrap {
        margin-bottom: 1rem;
    }
}
@media (max-width: 600px) {
    .store-header-hero {
        padding: 1.2rem 0.5rem 1rem 0.5rem;
    }
    .store-title {
        font-size: 1.3rem;
    }
    .store-logo-hero, .store-logo-placeholder {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
    .coupons-grid {
        grid-template-columns: 1fr;
        gap: 1.2rem;
    }
}
</style>

<script>
function revealStoreCode(button, affiliateLink) {
    // Show the code
    const codeSpan = button.nextElementSibling;
    codeSpan.style.display = 'inline-block';
    button.style.display = 'none';

    // Copy the code to clipboard
    const code = codeSpan.textContent.trim();
    navigator.clipboard.writeText(code).then(() => {
        // Store the copied code in sessionStorage
        sessionStorage.setItem('copiedCouponCode', code);

        // Open the affiliate link in a new tab
        if (affiliateLink) {
            window.open(affiliateLink, '_blank');
        }
    });
}
</script>
<?php include 'includes/footer.php'; ?>
