<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$id) {
    header('Location: pages.php');
    exit;
}

// Fetch page
$stmt = $conn->prepare("SELECT * FROM pages WHERE id = ?");
$stmt->execute([$id]);
$page = $stmt->fetch(PDO::FETCH_ASSOC);
if (!$page) {
    setMessage('danger', 'Page not found.');
    header('Location: pages.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $meta_title = trim($_POST['meta_title'] ?? '');
    $meta_description = trim($_POST['meta_description'] ?? '');
    $status = $_POST['status'] ?? 'published';
    $show_in_menu = isset($_POST['show_in_menu']) ? 1 : 0;
    $location = $_POST['location'] ?? $page['location'] ?? 'header';

    // Slugify if not provided
    if (!$slug && $title) {
        $slug = strtolower(preg_replace('/[^a-z0-9]+/i', '-', $title));
        $slug = trim($slug, '-');
    }

    // Validate
    $errors = [];
    if (!$title) $errors[] = 'Title is required.';
    if (!$slug) $errors[] = 'Slug is required.';
    if (!$content) $errors[] = 'Content is required.';

    // Check for unique slug (exclude current page)
    $stmt = $conn->prepare("SELECT id FROM pages WHERE slug = ? AND id != ?");
    $stmt->execute([$slug, $id]);
    if ($stmt->fetch()) $errors[] = 'Slug must be unique.';

    if (empty($errors)) {
        try {
            // Check if location column exists
            $stmt = $conn->query("SHOW COLUMNS FROM pages LIKE 'location'");
            $column_exists = $stmt->fetch();

            if ($column_exists) {
                // If location column exists, include it in the query
                $stmt = $conn->prepare("UPDATE pages SET title=?, slug=?, content=?, meta_title=?, meta_description=?, status=?, show_in_menu=?, location=? WHERE id=?");
                $stmt->execute([$title, $slug, $content, $meta_title, $meta_description, $status, $show_in_menu, $location, $id]);
            } else {
                // If location column doesn't exist, use the original query
                $stmt = $conn->prepare("UPDATE pages SET title=?, slug=?, content=?, meta_title=?, meta_description=?, status=?, show_in_menu=? WHERE id=?");
                $stmt->execute([$title, $slug, $content, $meta_title, $meta_description, $status, $show_in_menu, $id]);
            }

            setMessage('success', 'Page updated successfully!');
            header('Location: pages.php');
            exit;
        } catch (PDOException $e) {
            $errors[] = 'Database error: ' . $e->getMessage();
        }
    }
}

include 'includes/header.php';
?>
<div class="container-fluid py-4">
    <h1 class="h3 mb-4">Edit Page</h1>
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <?php foreach ($errors as $e) echo '<div>' . htmlspecialchars($e) . '</div>'; ?>
        </div>
    <?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label class="form-label">Title</label>
            <input type="text" name="title" class="form-control" value="<?php echo htmlspecialchars($_POST['title'] ?? $page['title']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Slug (URL)</label>
            <input type="text" name="slug" class="form-control" value="<?php echo htmlspecialchars($_POST['slug'] ?? $page['slug']); ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Content</label>
            <textarea name="content" id="editor" class="form-control" rows="10" required><?php echo htmlspecialchars($_POST['content'] ?? $page['content']); ?></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">Meta Title (SEO)</label>
            <input type="text" name="meta_title" class="form-control" value="<?php echo htmlspecialchars($_POST['meta_title'] ?? $page['meta_title']); ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">Meta Description (SEO)</label>
            <textarea name="meta_description" class="form-control" rows="2"><?php echo htmlspecialchars($_POST['meta_description'] ?? $page['meta_description']); ?></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">Status</label>
            <select name="status" class="form-select">
                <option value="published" <?php if (($_POST['status'] ?? $page['status']) === 'published') echo 'selected'; ?>>Published</option>
                <option value="draft" <?php if (($_POST['status'] ?? $page['status']) === 'draft') echo 'selected'; ?>>Draft</option>
            </select>
        </div>
        <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" name="show_in_menu" id="show_in_menu" value="1" <?php if (isset($_POST['show_in_menu'])) { if ($_POST['show_in_menu']) echo 'checked'; } else if ($page['show_in_menu']) echo 'checked'; ?>>
            <label class="form-check-label" for="show_in_menu">Show in Menu</label>
        </div>
        <div class="mb-3">
            <label class="form-label">Menu Location</label>
            <select name="location" class="form-select">
                <option value="header" <?php if (($_POST['location'] ?? $page['location'] ?? 'header') === 'header') echo 'selected'; ?>>Header</option>
                <option value="footer" <?php if (($_POST['location'] ?? $page['location'] ?? '') === 'footer') echo 'selected'; ?>>Footer</option>
                <option value="both" <?php if (($_POST['location'] ?? $page['location'] ?? '') === 'both') echo 'selected'; ?>>Both Header and Footer</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">Update Page</button>
        <a href="pages.php" class="btn btn-secondary">Cancel</a>
    </form>
</div>
<script>
    // Initialize CKEditor 4
    CKEDITOR.replace('editor', {
        height: 400,
        toolbarGroups: [
            { name: 'document', groups: [ 'mode', 'document', 'doctools' ] },
            { name: 'clipboard', groups: [ 'clipboard', 'undo' ] },
            { name: 'editing', groups: [ 'find', 'selection', 'spellchecker', 'editing' ] },
            { name: 'forms', groups: [ 'forms' ] },
            '/',
            { name: 'basicstyles', groups: [ 'basicstyles', 'cleanup' ] },
            { name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi', 'paragraph' ] },
            { name: 'links', groups: [ 'links' ] },
            { name: 'insert', groups: [ 'insert' ] },
            '/',
            { name: 'styles', groups: [ 'styles' ] },
            { name: 'colors', groups: [ 'colors' ] },
            { name: 'tools', groups: [ 'tools' ] },
            { name: 'others', groups: [ 'others' ] },
            { name: 'about', groups: [ 'about' ] }
        ],
        removeButtons: 'Save,NewPage,Preview,Print,Templates,Cut,Copy,Paste,PasteText,PasteFromWord,Find,Replace,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Strike,Subscript,Superscript,CopyFormatting,RemoveFormat,Outdent,Indent,CreateDiv,Blockquote,JustifyLeft,JustifyCenter,JustifyRight,JustifyBlock,BidiLtr,BidiRtl,Language,Anchor,Flash,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe,Maximize,ShowBlocks,About',
        extraPlugins: 'image2,uploadimage,uploadfile,filebrowser',
        removePlugins: 'image',
        contentsCss: [
            'https://cdn.ckeditor.com/4.20.0/full-all/contents.css',
            'https://cdn.ckeditor.com/4.20.0/full-all/plugins/tableselection/styles/tableselection.css'
        ],
        filebrowserUploadUrl: '<?php echo ADMIN_URL; ?>/upload.php',
        filebrowserImageUploadUrl: '<?php echo ADMIN_URL; ?>/upload.php?type=Images'
    });
</script>

<?php include 'includes/footer.php'; ?>
