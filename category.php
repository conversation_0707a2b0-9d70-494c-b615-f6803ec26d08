<?php
require_once 'includes/config.php';
require_once 'includes/db_queries.php';

// Get category slug from URL
$category_slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($category_slug)) {
    header("Location: index.php");
    exit();
}

// Pagination settings
$posts_per_page = 9;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$current_page = max(1, $current_page);
$offset = ($current_page - 1) * $posts_per_page;

// Get category details and posts
try {
    // Get category details
    $stmt = $conn->prepare("
        SELECT * FROM categories 
        WHERE slug = :slug AND status = 'active'
    ");
    $stmt->bindValue(':slug', $category_slug, PDO::PARAM_STR);
    $stmt->execute();
    $category = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$category) {
        header("Location: index.php");
        exit();
    }

    // Get total posts count for pagination
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total 
        FROM posts p 
        WHERE p.category_id = :category_id 
        AND p.status = 'published'
    ");
    $stmt->bindValue(':category_id', $category['id'], PDO::PARAM_INT);
    $stmt->execute();
    $total_posts = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_posts / $posts_per_page);
    
    // Ensure current page doesn't exceed total pages
    $current_page = min($current_page, $total_pages);

    // Get posts for current page
    $stmt = $conn->prepare("
        SELECT p.*, u.username as author_name 
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        WHERE p.category_id = :category_id 
        AND p.status = 'published' 
        ORDER BY p.created_at DESC 
        LIMIT :offset, :limit
    ");
    $stmt->bindValue(':category_id', $category['id'], PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $posts_per_page, PDO::PARAM_INT);
    $stmt->execute();
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    error_log("Error in category page: " . $e->getMessage());
    header("Location: index.php");
    exit();
}

include 'includes/header.php';
?>

<!-- Add custom CSS for category page -->
<style>
.category-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.category-title {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.category-description {
    color: #666;
    font-size: 1.1rem;
    max-width: 800px;
    margin: 0 auto;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.article {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.article:hover {
    transform: translateY(-5px);
}

.article-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.article-content {
    padding: 1.5rem;
}

.article-content h3 {
    margin: 0 0 1rem;
    font-size: 1.25rem;
}

.article-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.article-content p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.article-meta {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
}

.read-more {
    display: inline-block;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
}

.read-more:hover {
    color: var(--secondary-color);
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 3rem;
}

.pagination a, .pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    background: white;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--primary-color);
    color: white;
}

.pagination .current {
    background: var(--primary-color);
    color: white;
}

.no-posts {
    text-align: center;
    padding: 3rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #666;
}
</style>

<div class="container">
    <div class="main-content">
        <div class="content-area">
            <!-- Category Header -->
            <header class="category-header">
                <h1 class="category-title"><?php echo htmlspecialchars($category['name']); ?></h1>
                <?php if (!empty($category['description'])): ?>
                <div class="category-description">
                    <?php echo htmlspecialchars($category['description']); ?>
                </div>
                <?php endif; ?>
            </header>

            <?php if (!empty($posts)): ?>
            <!-- Articles Grid -->
            <div class="articles-grid">
                <?php foreach ($posts as $post): ?>
                <article class="article">
                    <img src="<?php echo !empty($post['featured_image']) ? 'uploads/posts/' . htmlspecialchars($post['featured_image']) : 'assets/images/default-post.jpg'; ?>" 
                         alt="<?php echo htmlspecialchars($post['title']); ?>" 
                         class="article-image">
                    <div class="article-content">
                        <div class="article-meta">
                            Par <?php echo htmlspecialchars($post['author_name']); ?> • 
                            <?php echo formatDate($post['created_at'], 'j F Y'); ?>
                        </div>
                        <h3>
                            <a href="post.php?slug=<?php echo $post['slug']; ?>">
                                <?php echo htmlspecialchars($post['title']); ?>
                            </a>
                        </h3>
                        <p><?php echo getExcerpt($post['excerpt']); ?></p>
                        <a href="post.php?slug=<?php echo $post['slug']; ?>" class="read-more">
                            Lire la suite
                        </a>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($current_page > 1): ?>
                    <a href="?slug=<?php echo $category_slug; ?>&page=<?php echo $current_page - 1; ?>">&laquo; Précédent</a>
                <?php endif; ?>

                <?php
                $start_page = max(1, $current_page - 2);
                $end_page = min($total_pages, $start_page + 4);
                $start_page = max(1, $end_page - 4);

                if ($start_page > 1) {
                    echo '<a href="?slug=' . $category_slug . '&page=1">1</a>';
                    if ($start_page > 2) {
                        echo '<span>...</span>';
                    }
                }

                for ($i = $start_page; $i <= $end_page; $i++) {
                    if ($i == $current_page) {
                        echo '<span class="current">' . $i . '</span>';
                    } else {
                        echo '<a href="?slug=' . $category_slug . '&page=' . $i . '">' . $i . '</a>';
                    }
                }

                if ($end_page < $total_pages) {
                    if ($end_page < $total_pages - 1) {
                        echo '<span>...</span>';
                    }
                    echo '<a href="?slug=' . $category_slug . '&page=' . $total_pages . '">' . $total_pages . '</a>';
                }

                if ($current_page < $total_pages): ?>
                    <a href="?slug=<?php echo $category_slug; ?>&page=<?php echo $current_page + 1; ?>">Suivant &raquo;</a>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <?php else: ?>
            <div class="no-posts">
                <h2>Aucun article disponible dans cette catégorie</h2>
                <p>Revenez plus tard pour découvrir de nouveaux articles.</p>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 