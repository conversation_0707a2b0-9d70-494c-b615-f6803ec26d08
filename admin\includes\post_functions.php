<?php
/**
 * Post management functions
 */

/**
 * Get all posts with their associated category and author information
 * @param int $page Current page number
 * @param int $per_page Posts per page
 * @return array Array of posts with category and author details
 */
function getAllPosts($page = 1, $per_page = 10) {
    global $conn;
    
    try {
        // Get total count
        $count_stmt = $conn->query("SELECT COUNT(*) FROM posts");
        $total_posts = $count_stmt->fetchColumn();
        
        // Calculate offset
        $offset = ($page - 1) * $per_page;
        
        // Get posts with category and author information
        $sql = "SELECT 
                    p.*, 
                    c.name as category_name,
                    COALESCE(u.username, 'Unknown') as author_name
                FROM posts p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN users u ON p.author_id = u.id 
                ORDER BY p.created_at DESC 
                LIMIT :limit OFFSET :offset";
                
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Ensure all required fields are present
        foreach ($posts as &$post) {
            $post['title'] = $post['title'] ?? '';
            $post['category_name'] = $post['category_name'] ?? 'Uncategorized';
            $post['author_name'] = $post['author_name'] ?? 'Unknown';
            $post['status'] = $post['status'] ?? 'draft';
            $post['created_at'] = $post['created_at'] ?? date('Y-m-d H:i:s');
            $post['excerpt'] = $post['excerpt'] ?? '';
            $post['featured_image'] = $post['featured_image'] ?? '';
        }
        
        return [
            'posts' => $posts,
            'total' => $total_posts
        ];
        
    } catch (PDOException $e) {
        error_log("Error fetching posts: " . $e->getMessage());
        return [
            'posts' => [],
            'total' => 0
        ];
    }
}

/**
 * Get a single post by ID
 * @param int $id Post ID
 * @return array|false Post data or false if not found
 */
function getPostById($id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM posts WHERE id = :id");
    $stmt->execute(['id' => $id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Get posts by category ID
 * @param int $categoryId Category ID
 * @return array Array of posts
 */
function getPostsByCategory($categoryId) {
    global $conn;
    
    $sql = "SELECT 
                p.*,
                c.name as category_name,
                u.username as author_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN users u ON p.author_id = u.id
            WHERE p.category_id = :category_id
            ORDER BY p.created_at DESC";
            
    try {
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching posts by category: " . $e->getMessage());
        return [];
    }
}

/**
 * Get published posts count by category
 * @param int $categoryId Category ID
 * @return int Number of published posts
 */
function getPublishedPostsCountByCategory($categoryId) {
    global $conn;
    
    $sql = "SELECT COUNT(*) FROM posts 
            WHERE category_id = :category_id 
            AND status = 'published'";
            
    try {
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        error_log("Error counting posts: " . $e->getMessage());
        return 0;
    }
}

/**
 * Add a new post
 * @param array $data Post data
 * @return int|false The ID of the newly created post or false on failure
 */
function addPost($data) {
    global $conn;
    
    $sql = "INSERT INTO posts (
                title, 
                slug, 
                category_id, 
                excerpt,
                content,
                featured_image,
                status,
                author_id
            ) VALUES (
                :title,
                :slug,
                :category_id,
                :excerpt,
                :content,
                :featured_image,
                :status,
                :author_id
            )";
            
    try {
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':title', $data['title'], PDO::PARAM_STR);
        $stmt->bindParam(':slug', $data['slug'], PDO::PARAM_STR);
        $stmt->bindParam(':category_id', $data['category_id'], PDO::PARAM_INT);
        $stmt->bindParam(':excerpt', $data['excerpt'], PDO::PARAM_STR);
        $stmt->bindParam(':content', $data['content'], PDO::PARAM_STR);
        $stmt->bindParam(':featured_image', $data['featured_image'], PDO::PARAM_STR);
        $stmt->bindParam(':status', $data['status'], PDO::PARAM_STR);
        $stmt->bindParam(':author_id', $_SESSION['admin_id'], PDO::PARAM_INT);
        
        $stmt->execute();
        return $conn->lastInsertId();
    } catch (PDOException $e) {
        error_log("Error adding post: " . $e->getMessage());
        return false;
    }
}

/**
 * Update an existing post
 * @param int $id Post ID
 * @param array $data Post data
 * @return boolean Success or failure
 */
function updatePost($id, $data) {
    global $conn;
    
    $sql = "UPDATE posts SET 
                title = :title,
                slug = :slug,
                category_id = :category_id,
                excerpt = :excerpt,
                content = :content,
                status = :status";
    
    // Only update featured image if a new one is provided
    if (!empty($data['featured_image'])) {
        $sql .= ", featured_image = :featured_image";
    }
    
    $sql .= " WHERE id = :id";
            
    try {
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':title', $data['title'], PDO::PARAM_STR);
        $stmt->bindParam(':slug', $data['slug'], PDO::PARAM_STR);
        $stmt->bindParam(':category_id', $data['category_id'], PDO::PARAM_INT);
        $stmt->bindParam(':excerpt', $data['excerpt'], PDO::PARAM_STR);
        $stmt->bindParam(':content', $data['content'], PDO::PARAM_STR);
        $stmt->bindParam(':status', $data['status'], PDO::PARAM_STR);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        if (!empty($data['featured_image'])) {
            $stmt->bindParam(':featured_image', $data['featured_image'], PDO::PARAM_STR);
        }
        
        return $stmt->execute();
    } catch (PDOException $e) {
        error_log("Error updating post: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete a post
 * @param int $id Post ID
 * @return boolean Success or failure
 */
function deletePost($id) {
    global $conn;
    
    // Get post data first
    $post = getPostById($id);
    if (!$post) {
        return false;
    }
    
    // Delete the featured image if it exists
    if ($post['featured_image']) {
        $image_path = '../../uploads/posts/' . $post['featured_image'];
        if (file_exists($image_path)) {
            unlink($image_path);
        }
    }
    
    // Delete the post
    $stmt = $conn->prepare("DELETE FROM posts WHERE id = :id");
    return $stmt->execute(['id' => $id]);
}

/**
 * Validate post data
 * 
 * @param array $data Post data to validate
 * @return array Array containing validation status and errors
 */
function validatePostData($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['title'])) {
        $errors[] = 'Title is required';
    }
    
    if (empty($data['category_id'])) {
        $errors[] = 'Category is required';
    }
    
    // Validate slug uniqueness if provided
    if (!empty($data['slug'])) {
        global $conn;
        $stmt = $conn->prepare("SELECT id FROM posts WHERE slug = :slug AND id != :id");
        $stmt->execute([
            'slug' => $data['slug'],
            'id' => $data['id'] ?? 0
        ]);
        
        if ($stmt->fetch()) {
            $errors[] = 'Slug must be unique';
        }
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
} 