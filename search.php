<?php
require_once 'includes/config.php';
require_once 'includes/db_queries.php';

// Get search query
$query = isset($_GET['q']) ? trim($_GET['q']) : '';
$type = isset($_GET['type']) ? $_GET['type'] : 'all';

// Validate search query
if (empty($query)) {
    header('Location: /');
    exit;
}

// Initialize results arrays
$pages_results = [];
$coupons_results = [];
$stores_results = [];
$total_results = 0;

// Search function for pages
function searchPages($query) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT * FROM pages 
            WHERE status = 'published' 
            AND (
                title LIKE :query 
                OR content LIKE :query 
                OR meta_title LIKE :query 
                OR meta_description LIKE :query
            )
            ORDER BY created_at DESC
        ");
        $stmt->bindValue(':query', "%$query%", PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error searching pages: " . $e->getMessage());
        return [];
    }
}

// Search function for coupons
function searchCoupons($query) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT c.*, s.name as store_name, s.logo as store_logo, cat.name as category_name, cat.icon as category_icon
            FROM coupons c
            JOIN stores s ON c.store_id = s.id
            LEFT JOIN categories cat ON c.category_id = cat.id
            WHERE c.status = 'active'
            AND c.start_date <= NOW() 
            AND c.end_date >= NOW()
            AND (
                c.code LIKE :query 
                OR c.description LIKE :query 
                OR s.name LIKE :query
                OR cat.name LIKE :query
            )
            ORDER BY c.created_at DESC
        ");
        $stmt->bindValue(':query', "%$query%", PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error searching coupons: " . $e->getMessage());
        return [];
    }
}

// Search function for stores
function searchStores($query) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT s.*, 
            COUNT(c.id) as total_coupons,
            SUM(CASE WHEN c.status = 'active' 
                     AND c.start_date <= NOW() 
                     AND c.end_date >= NOW() 
                THEN 1 ELSE 0 END) as active_coupons
            FROM stores s
            LEFT JOIN coupons c ON s.id = c.store_id
            WHERE s.status = 'active'
            AND (
                s.name LIKE :query 
                OR s.description LIKE :query
            )
            GROUP BY s.id
            ORDER BY s.name ASC
        ");
        $stmt->bindValue(':query', "%$query%", PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Error searching stores: " . $e->getMessage());
        return [];
    }
}

// Get excerpt for search results
function getSearchExcerpt($text, $query, $length = 150) {
    $text = strip_tags($text);
    
    // Find position of the query in the text
    $pos = stripos($text, $query);
    
    // If query is found, create excerpt around it
    if ($pos !== false) {
        $start = max(0, $pos - $length / 2);
        $excerpt = substr($text, $start, $length);
        
        // Add ellipsis if needed
        if ($start > 0) {
            $excerpt = '...' . $excerpt;
        }
        if ($start + $length < strlen($text)) {
            $excerpt .= '...';
        }
        
        // Highlight the query
        return preg_replace('/(' . preg_quote($query, '/') . ')/i', '<span class="highlight">$1</span>', $excerpt);
    }
    
    // If query not found, return beginning of text
    return substr($text, 0, $length) . (strlen($text) > $length ? '...' : '');
}

// Perform search based on type
if ($type == 'all' || $type == 'pages') {
    $pages_results = searchPages($query);
    $total_results += count($pages_results);
}

if ($type == 'all' || $type == 'coupons') {
    $coupons_results = searchCoupons($query);
    $total_results += count($coupons_results);
}

if ($type == 'all' || $type == 'stores') {
    $stores_results = searchStores($query);
    $total_results += count($stores_results);
}

// Include header
include 'includes/header.php';
?>

<div class="container">
    <div class="main-content">
        <div class="content-area">
            <div class="search-results-header">
                <h1>Résultats de recherche pour "<?php echo htmlspecialchars($query); ?>"</h1>
                <p><?php echo $total_results; ?> résultat(s) trouvé(s)</p>
                
                <div class="search-filters">
                    <form action="search.php" method="GET" class="search-form">
                        <input type="hidden" name="q" value="<?php echo htmlspecialchars($query); ?>">
                        <div class="search-type-filters">
                            <button type="submit" name="type" value="all" class="filter-btn <?php echo $type == 'all' ? 'active' : ''; ?>">Tous</button>
                            <button type="submit" name="type" value="coupons" class="filter-btn <?php echo $type == 'coupons' ? 'active' : ''; ?>">Coupons</button>
                            <button type="submit" name="type" value="stores" class="filter-btn <?php echo $type == 'stores' ? 'active' : ''; ?>">Boutiques</button>
                            <button type="submit" name="type" value="pages" class="filter-btn <?php echo $type == 'pages' ? 'active' : ''; ?>">Pages</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php if ($total_results == 0): ?>
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h3>Aucun résultat trouvé</h3>
                    <p>Essayez avec d'autres mots-clés ou consultez nos catégories populaires.</p>
                    <a href="/" class="btn-primary">Retour à l'accueil</a>
                </div>
            <?php else: ?>
                
                <!-- Coupons Results -->
                <?php if (($type == 'all' || $type == 'coupons') && !empty($coupons_results)): ?>
                    <section class="search-section">
                        <h2 class="section-title"><i class="fas fa-ticket-alt"></i> Coupons (<?php echo count($coupons_results); ?>)</h2>
                        <div class="coupons-grid">
                            <?php foreach ($coupons_results as $coupon): ?>
                                <div class="coupon-article coupon-card">
                                    <div class="coupon-article-header">
                                        <div class="coupon-meta">
                                            <span class="discount-badge <?php echo $coupon['discount_type'] === 'percentage' ? 'percentage' : 'fixed'; ?>">
                                                <?php if ($coupon['discount_type'] === 'percentage'): ?>
                                                    <?php echo (int)$coupon['discount_value']; ?>% OFF
                                                <?php else: ?>
                                                    <?php echo number_format($coupon['discount_value'], 0, ',', ' '); ?> <?php echo htmlspecialchars($coupon['currency'] ?? ''); ?> OFF
                                                <?php endif; ?>
                                            </span>
                                            <?php if (!empty($coupon['category_name'])): ?>
                                            <span class="category-badge">
                                                <i class="<?php echo !empty($coupon['category_icon']) ? $coupon['category_icon'] : 'fas fa-tag'; ?>"></i>
                                                <?php echo htmlspecialchars($coupon['category_name']); ?>
                                            </span>
                                            <?php endif; ?>
                                            <span class="coupon-date">
                                                <i class="fas fa-calendar-alt"></i>
                                                Valide jusqu'au <?php echo date('d/m/Y', strtotime($coupon['end_date'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="coupon-article-content">
                                        <h3>
                                            <?php 
                                            // Generate dynamic title if description is empty or generic
                                            if (empty(trim($coupon['description'])) || strtolower(trim($coupon['description'])) == 'coupon code' || strtolower(trim($coupon['description'])) == 'code promo') {
                                                if ($coupon['discount_type'] === 'percentage') {
                                                    echo htmlspecialchars($coupon['discount_value'] . '% de réduction chez ' . $coupon['store_name']);
                                                } else {
                                                    echo htmlspecialchars($coupon['discount_value'] . '€ de réduction chez ' . $coupon['store_name']);
                                                }
                                            } else {
                                                echo htmlspecialchars($coupon['description']);
                                            }
                                            ?>
                                        </h3>
                                        <div class="coupon-details">
                                            <div class="coupon-detail"><i class="fas fa-store"></i> <strong>Boutique:</strong> <?php echo htmlspecialchars($coupon['store_name']); ?></div>
                                            <?php if (!empty($coupon['code'])): ?>
                                            <div class="coupon-detail coupon-code-detail">
                                                <i class="fas fa-ticket-alt"></i> <strong>Code:</strong>
                                                <button class="reveal-code-btn" onclick="revealStoreCode(this, '<?php echo htmlspecialchars($coupon['affiliate_link'] ?: '#'); ?>')">Afficher le code</button>
                                                <span class="coupon-code" style="display:none;"> <?php echo htmlspecialchars($coupon['code']); ?></span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="coupon-article-footer">
                                        <a href="/coupon.php?id=<?php echo $coupon['id']; ?>" class="btn-primary coupon-view-btn">
                                            <i class="fas fa-eye"></i> Voir le détail
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
                
                <!-- Stores Results -->
                <?php if (($type == 'all' || $type == 'stores') && !empty($stores_results)): ?>
                    <section class="search-section">
                        <h2 class="section-title"><i class="fas fa-store"></i> Boutiques (<?php echo count($stores_results); ?>)</h2>
                        <div class="stores-grid">
                            <?php foreach ($stores_results as $store): ?>
                                <div class="store-card">
                                    <div class="store-logo">
                                        <?php if (!empty($store['logo'])): ?>
                                            <img src="/uploads/stores/<?php echo htmlspecialchars($store['logo']); ?>" alt="<?php echo htmlspecialchars($store['name']); ?>">
                                        <?php else: ?>
                                            <div class="store-logo-placeholder"><i class="fas fa-store"></i></div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="store-info">
                                        <h3><?php echo htmlspecialchars($store['name']); ?></h3>
                                        <?php if (!empty($store['description'])): ?>
                                            <p><?php echo getSearchExcerpt($store['description'], $query); ?></p>
                                        <?php endif; ?>
                                        <div class="store-meta">
                                            <span class="coupon-count"><?php echo (int)$store['active_coupons']; ?> coupons actifs</span>
                                        </div>
                                    </div>
                                    <div class="store-actions">
                                        <a href="/store.php?slug=<?php echo htmlspecialchars($store['slug']); ?>" class="btn-primary">Voir les coupons</a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
                
                <!-- Pages Results -->
                <?php if (($type == 'all' || $type == 'pages') && !empty($pages_results)): ?>
                    <section class="search-section">
                        <h2 class="section-title"><i class="fas fa-file-alt"></i> Pages (<?php echo count($pages_results); ?>)</h2>
                        <div class="pages-results">
                            <?php foreach ($pages_results as $page): ?>
                                <div class="page-result-item">
                                    <h3><a href="/page/<?php echo htmlspecialchars($page['slug']); ?>"><?php echo htmlspecialchars($page['title']); ?></a></h3>
                                    <div class="page-excerpt">
                                        <?php echo getSearchExcerpt($page['content'], $query, 200); ?>
                                    </div>
                                    <div class="page-meta">
                                        <span class="page-date"><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($page['created_at'])); ?></span>
                                        <a href="/page/<?php echo htmlspecialchars($page['slug']); ?>" class="read-more">Lire la suite <i class="fas fa-arrow-right"></i></a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
                
            <?php endif; ?>
        </div>
        
        <?php include 'includes/sidebar.php'; ?>
    </div>
</div>

<style>
    .search-results-header {
        margin-bottom: 2rem;
    }
    
    .search-results-header h1 {
        font-size: 1.8rem;
        color: #0A0A4A;
        margin-bottom: 0.5rem;
    }
    
    .search-filters {
        margin: 1.5rem 0;
    }
    
    .search-type-filters {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .filter-btn {
        background: #f5f5f5;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.2s;
    }
    
    .filter-btn.active {
        background: #0A0A4A;
        color: white;
    }
    
    .filter-btn:hover {
        background: #e0e0e0;
    }
    
    .filter-btn.active:hover {
        background: #0A0A4A;
    }
    
    .search-section {
        margin-bottom: 3rem;
    }
    
    .section-title {
        font-size: 1.4rem;
        color: #0A0A4A;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.7rem;
    }
    
    .section-title i {
        color: #FF7F00;
    }
    
    .highlight {
        background-color: #FFEB3B;
        padding: 0 2px;
        font-weight: bold;
    }
    
    /* Store Card Styles */
    .stores-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .store-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .store-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .store-logo {
        display: flex;
        justify-content: center;
        margin-bottom: 1rem;
    }
    
    .store-logo img {
        width: 100px;
        height: 100px;
        object-fit: contain;
        border-radius: 10px;
        padding: 0.5rem;
        background: #f9f9f9;
    }
    
    .store-logo-placeholder {
        width: 100px;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        border-radius: 10px;
        font-size: 2.5rem;
        color: #ccc;
    }
    
    .store-info {
        flex-grow: 1;
    }
    
    .store-info h3 {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
        color: #0A0A4A;
    }
    
    .store-info p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 1rem;
    }
    
    .store-meta {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .coupon-count {
        background: #f0f0f0;
        padding: 0.3rem 0.7rem;
        border-radius: 20px;
        font-size: 0.8rem;
        color: #555;
    }
    
    .store-actions {
        margin-top: auto;
    }
    
    /* Page Result Styles */
    .pages-results {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .page-result-item {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        padding: 1.5rem;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .page-result-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .page-result-item h3 {
        font-size: 1.3rem;
        margin-bottom: 0.7rem;
    }
    
    .page-result-item h3 a {
        color: #0A0A4A;
        text-decoration: none;
        transition: color 0.2s;
    }
    
    .page-result-item h3 a:hover {
        color: #FF7F00;
    }
    
    .page-excerpt {
        color: #555;
        margin-bottom: 1rem;
        line-height: 1.5;
    }
    
    .page-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
    }
    
    .page-date {
        color: #888;
    }
    
    .page-date i {
        margin-right: 0.3rem;
    }
    
    .read-more {
        color: #FF7F00;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        transition: color 0.2s;
    }
    
    .read-more:hover {
        color: #0A0A4A;
    }
    
    /* No Results Styles */
    .no-results {
        text-align: center;
        padding: 3rem 1.5rem;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(10,10,74,0.07);
        margin-top: 2rem;
    }
    
    .no-results i {
        font-size: 2.5rem;
        color: #FF7F00;
        margin-bottom: 1rem;
    }
    
    .no-results h3 {
        font-size: 1.3rem;
        color: #0A0A4A;
        margin-bottom: 1rem;
    }
    
    .no-results p {
        color: #888;
        margin-bottom: 2rem;
    }
    
    .no-results .btn-primary {
        max-width: 220px;
        margin: 0 auto;
    }
    
    @media (max-width: 768px) {
        .stores-grid {
            grid-template-columns: 1fr;
        }
        
        .filter-btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
    }
</style>

<script>
function revealStoreCode(button, affiliateLink) {
    // Show the code
    const codeSpan = button.nextElementSibling;
    codeSpan.style.display = 'inline-block';
    button.style.display = 'none';
    
    // Copy the code to clipboard
    const code = codeSpan.textContent.trim();
    navigator.clipboard.writeText(code).then(() => {
        // Store the copied code in sessionStorage
        sessionStorage.setItem('copiedCouponCode', code);
        
        // Open the affiliate link in a new tab
        if (affiliateLink && affiliateLink !== '#') {
            window.open(affiliateLink, '_blank');
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?>
