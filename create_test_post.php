<?php
require_once 'includes/config.php';

try {
    // Insert a test post
    $stmt = $conn->prepare("
        INSERT INTO posts (
            title, slug, content, excerpt, featured_image, 
            category_id, author_id, status, meta_title, 
            meta_description, meta_keywords, views
        ) VALUES (
            'Test Post Title',
            'test-post-title',
            'This is a test post content. It contains some sample text to demonstrate how posts look on the website.',
            'This is a brief excerpt of the test post that will appear in the preview.',
            'test-image.jpg',
            1,
            1,
            'published',
            'Test Post Title - TripNest',
            'This is a test post description for SEO purposes.',
            'test, post, sample',
            0
        )
    ");
    $stmt->execute();
    echo "Test post created successfully!";
} catch(PDOException $e) {
    echo "Error creating test post: " . $e->getMessage();
}
?> 