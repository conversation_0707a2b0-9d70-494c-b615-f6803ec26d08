<?php
require_once 'includes/config.php';
require_once 'includes/db_queries.php';

$slug = isset($_GET['slug']) ? trim($_GET['slug']) : '';
if (!$slug) {
    header('Location: /');
    exit;
}

$stmt = $conn->prepare("SELECT * FROM pages WHERE slug = ? AND status = 'published' LIMIT 1");
$stmt->execute([$slug]);
$page = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$page) {
    include 'includes/header.php';
    echo '<div class="container py-5"><h1>404 - Page Not Found</h1><p>The page you are looking for does not exist.</p></div>';
    include 'includes/footer.php';
    exit;
}

$meta_title = $page['meta_title'] ?: $page['title'];
$meta_description = $page['meta_description'] ?: '';

?><!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($meta_title); ?></title>
    <?php if ($meta_description): ?>
    <meta name="description" content="<?php echo htmlspecialchars($meta_description); ?>">
    <?php endif; ?>
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        /* Rich Text Content Styling */
        .page-content {
            line-height: 1.6;
            color: #333;
        }
        .page-content h1, .page-content h2, .page-content h3,
        .page-content h4, .page-content h5, .page-content h6 {
            margin-top: 1.5em;
            margin-bottom: 0.75em;
            color: #0A0A4A;
        }
        .page-content h1 { font-size: 2.2em; }
        .page-content h2 { font-size: 1.8em; }
        .page-content h3 { font-size: 1.5em; }
        .page-content h4 { font-size: 1.3em; }
        .page-content h5 { font-size: 1.1em; }
        .page-content h6 { font-size: 1em; }

        .page-content p {
            margin-bottom: 1.2em;
        }
        .page-content ul, .page-content ol {
            margin-bottom: 1.2em;
            padding-left: 2em;
        }
        .page-content li {
            margin-bottom: 0.5em;
        }
        .page-content blockquote {
            border-left: 4px solid #FF7F00;
            padding-left: 1em;
            margin-left: 0;
            color: #555;
            font-style: italic;
        }
        .page-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 1em 0;
        }
        .page-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        .page-content table, .page-content th, .page-content td {
            border: 1px solid #ddd;
        }
        .page-content th, .page-content td {
            padding: 0.75em;
            text-align: left;
        }
        .page-content th {
            background-color: #f5f5f5;
        }
        .page-content a {
            color: #FF7F00;
            text-decoration: none;
        }
        .page-content a:hover {
            text-decoration: underline;
        }
        .page-content code {
            background-color: #f5f5f5;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: monospace;
        }
        .page-content pre {
            background-color: #f5f5f5;
            padding: 1em;
            border-radius: 4px;
            overflow-x: auto;
            font-family: monospace;
        }
        .page-content figure {
            margin: 1em 0;
        }
        .page-content figcaption {
            font-size: 0.9em;
            color: #666;
            text-align: center;
            margin-top: 0.5em;
        }
        .page-content .image-style-side {
            float: right;
            margin-left: 1.5em;
            max-width: 50%;
        }
        .page-content .image-style-align-left {
            float: left;
            margin-right: 1.5em;
            max-width: 50%;
        }
        .page-content .image-style-align-center {
            margin-left: auto;
            margin-right: auto;
            display: block;
        }
        @media (max-width: 768px) {
            .page-content .image-style-side,
            .page-content .image-style-align-left {
                float: none;
                margin: 1em auto;
                max-width: 100%;
                display: block;
            }
        }
    </style>
</head>
<body>
<?php include 'includes/header.php'; ?>
<div class="container py-5">
    <h1><?php echo htmlspecialchars($page['title']); ?></h1>
    <div class="page-content">
        <?php echo $page['content']; ?>
    </div>
</div>
<?php include 'includes/footer.php'; ?>
</body>
</html>
