<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/post_functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized access']));
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die(json_encode(['success' => false, 'message' => 'Invalid request method']));
}

// Validate required fields
if (empty($_POST['title']) || empty($_POST['category_id'])) {
    die(json_encode(['success' => false, 'message' => 'Title and category are required']));
}

// Create uploads directory if it doesn't exist
$upload_dir = '../../uploads/posts/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Handle file upload
$featured_image = '';
if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === 0) {
    $result = uploadFile($_FILES['featured_image'], $upload_dir);
    if ($result) {
        $featured_image = $result;
    }
}

// Generate slug if not provided
$slug = !empty($_POST['slug']) ? sanitize($_POST['slug']) : generateSlug($_POST['title']);

// Prepare post data
$post_data = [
    'title' => sanitize($_POST['title']),
    'slug' => $slug,
    'category_id' => (int)$_POST['category_id'],
    'excerpt' => isset($_POST['excerpt']) ? sanitize($_POST['excerpt']) : '',
    'content' => isset($_POST['content']) ? $_POST['content'] : '', // Don't sanitize content to allow HTML
    'featured_image' => $featured_image,
    'status' => isset($_POST['status']) ? sanitize($_POST['status']) : 'draft',
    'meta_title' => isset($_POST['meta_title']) ? sanitize($_POST['meta_title']) : sanitize($_POST['title']),
    'meta_description' => isset($_POST['meta_description']) ? sanitize($_POST['meta_description']) : (isset($_POST['excerpt']) ? sanitize($_POST['excerpt']) : ''),
    'meta_keywords' => isset($_POST['meta_keywords']) ? sanitize($_POST['meta_keywords']) : ''
];

// Add the post
$sql = "INSERT INTO posts (
            title, 
            slug, 
            category_id, 
            excerpt,
            content,
            featured_image,
            status,
            author_id,
            meta_title,
            meta_description,
            meta_keywords,
            created_at,
            updated_at
        ) VALUES (
            :title,
            :slug,
            :category_id,
            :excerpt,
            :content,
            :featured_image,
            :status,
            :author_id,
            :meta_title,
            :meta_description,
            :meta_keywords,
            NOW(),
            NOW()
        )";
        
try {
    $stmt = $conn->prepare($sql);
    
    $stmt->bindParam(':title', $post_data['title'], PDO::PARAM_STR);
    $stmt->bindParam(':slug', $post_data['slug'], PDO::PARAM_STR);
    $stmt->bindParam(':category_id', $post_data['category_id'], PDO::PARAM_INT);
    $stmt->bindParam(':excerpt', $post_data['excerpt'], PDO::PARAM_STR);
    $stmt->bindParam(':content', $post_data['content'], PDO::PARAM_STR);
    $stmt->bindParam(':featured_image', $post_data['featured_image'], PDO::PARAM_STR);
    $stmt->bindParam(':status', $post_data['status'], PDO::PARAM_STR);
    $stmt->bindParam(':author_id', $_SESSION['admin_id'], PDO::PARAM_INT);
    $stmt->bindParam(':meta_title', $post_data['meta_title'], PDO::PARAM_STR);
    $stmt->bindParam(':meta_description', $post_data['meta_description'], PDO::PARAM_STR);
    $stmt->bindParam(':meta_keywords', $post_data['meta_keywords'], PDO::PARAM_STR);
    
    $stmt->execute();
    $post_id = $conn->lastInsertId();
    
    echo json_encode([
        'success' => true,
        'message' => 'Post added successfully',
        'post_id' => $post_id
    ]);
} catch (PDOException $e) {
    error_log("Error adding post: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to add post: ' . $e->getMessage()
    ]);
} 