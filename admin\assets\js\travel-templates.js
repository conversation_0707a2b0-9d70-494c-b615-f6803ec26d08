/**
 * TripNest Travel Blog Templates for CKEditor
 * 
 * This file defines templates specifically designed for travel blog content in CKEditor.
 */

// Register templates
CKEDITOR.addTemplates('travel', {
    // The name of the templates group
    imagesPath: CKEDITOR.getUrl(CKEDITOR.plugins.getPath('templates') + 'templates/images/'),
    
    // The templates definitions
    templates: [
        {
            title: 'Destination Overview',
            image: 'template1.gif',
            description: 'A template for introducing a travel destination',
            html: '<div class="destination-overview">' +
                  '<h2 class="destination-header">Destination Name</h2>' +
                  '<p><span class="location">Location</span> is a wonderful place to visit because...</p>' +
                  '<div class="travel-tip"><h4>Travel Tip</h4><p>Important information for travelers...</p></div>' +
                  '</div>'
        },
        {
            title: 'Itinerary Day',
            image: 'template2.gif',
            description: 'A template for describing a day in your itinerary',
            html: '<div class="itinerary-day">' +
                  '<h3>Day X: Activity Highlight</h3>' +
                  '<p><strong>Morning:</strong> Description of morning activities...</p>' +
                  '<p><strong>Afternoon:</strong> Description of afternoon activities...</p>' +
                  '<p><strong>Evening:</strong> Description of evening activities...</p>' +
                  '<div class="price-info">Cost estimate: <span class="price">$XX</span></div>' +
                  '</div>'
        },
        {
            title: 'Accommodation Review',
            image: 'template3.gif',
            description: 'A template for hotel or accommodation reviews',
            html: '<div class="accommodation-review">' +
                  '<h3>Accommodation Name</h3>' +
                  '<p><strong>Location:</strong> Description of location</p>' +
                  '<p><strong>Price Range:</strong> <span class="price">$XX-$XX</span></p>' +
                  '<p><strong>Facilities:</strong> List of available facilities</p>' +
                  '<p><strong>Pros:</strong> What was good about this place</p>' +
                  '<p><strong>Cons:</strong> What could be improved</p>' +
                  '<p><strong>Verdict:</strong> Overall assessment</p>' +
                  '</div>'
        },
        {
            title: 'Restaurant Review',
            image: 'template1.gif',
            description: 'A template for restaurant reviews',
            html: '<div class="restaurant-review">' +
                  '<h3>Restaurant Name</h3>' +
                  '<p><strong>Cuisine:</strong> Type of cuisine</p>' +
                  '<p><strong>Price Range:</strong> <span class="price">$-$$$</span></p>' +
                  '<p><strong>Must Try Dishes:</strong> List of recommended dishes</p>' +
                  '<p><strong>Atmosphere:</strong> Description of restaurant ambiance</p>' +
                  '<p><strong>Service:</strong> Quality of service</p>' +
                  '<p><strong>Rating:</strong> ★★★★☆</p>' +
                  '</div>'
        },
        {
            title: 'Travel Checklist',
            image: 'template2.gif',
            description: 'A checklist for travelers',
            html: '<div class="travel-checklist">' +
                  '<h3>Travel Checklist for <span class="location">Destination</span></h3>' +
                  '<h4>Essential Documents</h4>' +
                  '<ul>' +
                  '<li>Passport/ID</li>' +
                  '<li>Visa documents</li>' +
                  '<li>Travel insurance information</li>' +
                  '<li>Flight/train tickets</li>' +
                  '<li>Hotel booking confirmation</li>' +
                  '</ul>' +
                  '<h4>Packing List</h4>' +
                  '<ul>' +
                  '<li>Clothing items</li>' +
                  '<li>Toiletries</li>' +
                  '<li>Electronics</li>' +
                  '<li>Medications</li>' +
                  '</ul>' +
                  '</div>'
        },
        {
            title: 'Photo Gallery Section',
            image: 'template3.gif',
            description: 'A template for showcasing multiple photos',
            html: '<div class="photo-gallery">' +
                  '<h3>Photo Gallery: <span class="location">Destination or Activity Name</span></h3>' +
                  '<div class="gallery-description"><p>Brief description of the photos and what they showcase...</p></div>' +
                  '<div class="row">' +
                  '<div class="col-md-4"><figure><img src="placeholder.jpg" alt="Description" style="width:100%" /><figcaption>Caption for image 1</figcaption></figure></div>' +
                  '<div class="col-md-4"><figure><img src="placeholder.jpg" alt="Description" style="width:100%" /><figcaption>Caption for image 2</figcaption></figure></div>' +
                  '<div class="col-md-4"><figure><img src="placeholder.jpg" alt="Description" style="width:100%" /><figcaption>Caption for image 3</figcaption></figure></div>' +
                  '</div>' +
                  '</div>'
        },
        {
            title: 'Transportation Guide',
            image: 'template1.gif',
            description: 'Information about transportation options',
            html: '<div class="transportation-guide">' +
                  '<h3>Getting Around <span class="location">Destination</span></h3>' +
                  '<div class="transport-option">' +
                  '<h4><i class="fas fa-plane"></i> By Air</h4>' +
                  '<p>Information about flights, airports, airlines...</p>' +
                  '<p><strong>Cost:</strong> <span class="price">$XX-$XX</span></p>' +
                  '</div>' +
                  '<div class="transport-option">' +
                  '<h4><i class="fas fa-train"></i> By Train</h4>' +
                  '<p>Information about train services...</p>' +
                  '<p><strong>Cost:</strong> <span class="price">$XX-$XX</span></p>' +
                  '</div>' +
                  '<div class="transport-option">' +
                  '<h4><i class="fas fa-bus"></i> By Bus</h4>' +
                  '<p>Information about bus services...</p>' +
                  '<p><strong>Cost:</strong> <span class="price">$XX-$XX</span></p>' +
                  '</div>' +
                  '<div class="transport-option">' +
                  '<h4><i class="fas fa-car"></i> By Car</h4>' +
                  '<p>Information about driving, car rentals...</p>' +
                  '<p><strong>Cost:</strong> <span class="price">$XX-$XX</span></p>' +
                  '</div>' +
                  '</div>'
        },
        {
            title: 'Travel FAQ',
            image: 'template2.gif',
            description: 'Frequently asked questions section',
            html: '<div class="travel-faq">' +
                  '<h3>Frequently Asked Questions about <span class="location">Destination</span></h3>' +
                  '<div class="faq-item">' +
                  '<h4>When is the best time to visit?</h4>' +
                  '<p>Answer with seasonal information, weather patterns, etc.</p>' +
                  '</div>' +
                  '<div class="faq-item">' +
                  '<h4>Is it expensive?</h4>' +
                  '<p>Answer with cost information, budget tips, etc.</p>' +
                  '</div>' +
                  '<div class="faq-item">' +
                  '<h4>Is it safe for tourists?</h4>' +
                  '<p>Answer with safety information, areas to avoid, etc.</p>' +
                  '</div>' +
                  '<div class="faq-item">' +
                  '<h4>What languages are spoken?</h4>' +
                  '<p>Answer with language information, useful phrases, etc.</p>' +
                  '</div>' +
                  '</div>'
        },
        {
            title: 'Local Food Guide',
            image: 'template3.gif',
            description: 'Guide to local cuisine and dishes',
            html: '<div class="food-guide">' +
                  '<h3>What to Eat in <span class="location">Destination</span></h3>' +
                  '<div class="food-item">' +
                  '<h4>Dish Name</h4>' +
                  '<p>Description of the dish, ingredients, taste profile, etc.</p>' +
                  '<p><strong>Where to try it:</strong> Restaurant recommendations</p>' +
                  '<p><strong>Price range:</strong> <span class="price">$XX-$XX</span></p>' +
                  '</div>' +
                  '<div class="food-item">' +
                  '<h4>Dish Name</h4>' +
                  '<p>Description of the dish, ingredients, taste profile, etc.</p>' +
                  '<p><strong>Where to try it:</strong> Restaurant recommendations</p>' +
                  '<p><strong>Price range:</strong> <span class="price">$XX-$XX</span></p>' +
                  '</div>' +
                  '<div class="food-tip"><p><strong>Tip:</strong> Special advice about local dining customs, etiquette, etc.</p></div>' +
                  '</div>'
        },
        {
            title: 'Travel Budget Breakdown',
            image: 'template1.gif',
            description: 'Detailed breakdown of travel expenses',
            html: '<div class="budget-breakdown">' +
                  '<h3>Cost of Traveling to <span class="location">Destination</span></h3>' +
                  '<table class="expense-table" style="width:100%; border-collapse: collapse;">' +
                  '<tr><th style="border:1px solid #ddd; padding:8px; text-align:left;">Expense Category</th><th style="border:1px solid #ddd; padding:8px; text-align:left;">Budget Option</th><th style="border:1px solid #ddd; padding:8px; text-align:left;">Mid-Range Option</th><th style="border:1px solid #ddd; padding:8px; text-align:left;">Luxury Option</th></tr>' +
                  '<tr><td style="border:1px solid #ddd; padding:8px;">Accommodation</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/night</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/night</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/night</td></tr>' +
                  '<tr><td style="border:1px solid #ddd; padding:8px;">Food</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td></tr>' +
                  '<tr><td style="border:1px solid #ddd; padding:8px;">Transportation</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td></tr>' +
                  '<tr><td style="border:1px solid #ddd; padding:8px;">Activities</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td><td style="border:1px solid #ddd; padding:8px;"><span class="price">$XX</span>/day</td></tr>' +
                  '</table>' +
                  '<div class="budget-tip"><p><strong>Money-saving tip:</strong> Advice for saving money when traveling to this destination...</p></div>' +
                  '</div>'
        }
    ]
});
