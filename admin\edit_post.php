<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/category_functions.php';
require_once 'includes/post_functions.php';

// Check if user is logged in
requireLogin();

// Get post ID from URL
$post_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get post data
$post = getPostById($post_id);
if (!$post) {
    setMessage('error', 'Post not found.');
    header('Location: posts.php');
    exit;
}

// Get all categories
$categories = getAllCategories();

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit Post</h1>
        <a href="posts.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Posts
        </a>
    </div>

    <div class="row">
        <div class="col-md-9">
            <!-- Main Content Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="editPostForm" enctype="multipart/form-data">
                        <input type="hidden" name="id" value="<?php echo $post['id']; ?>">
                        <div class="mb-4">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" class="form-control form-control-lg" id="title" name="title" value="<?php echo htmlspecialchars($post['title']); ?>" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="content" class="form-label">Content</label>
                            <div class="editor-container">
                                <!-- Quill Editor Container -->
                                <div id="quill-editor" style="height: 400px; border: 1px solid #ced4da; border-radius: 0.375rem;"></div>
                                <!-- Hidden textarea for form submission -->
                                <textarea id="content" name="content" style="display: none;"><?php echo htmlspecialchars($post['content']); ?></textarea>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="excerpt" class="form-label">Excerpt</label>
                            <textarea class="form-control" id="excerpt" name="excerpt" rows="3"><?php echo htmlspecialchars($post['excerpt']); ?></textarea>
                            <div class="form-text">A short summary of the content. Will be used in blog listings and social media sharing.</div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="slug" class="form-label">URL Slug</label>
                        <input type="text" class="form-control" id="slug" name="slug" form="editPostForm" value="<?php echo htmlspecialchars($post['slug']); ?>" required>
                        <div class="form-text">The URL-friendly version of the title.</div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title" form="editPostForm" value="<?php echo htmlspecialchars($post['meta_title']); ?>">
                        <div class="form-text">
                            <span id="metaTitleCount">0</span>/60 characters. The title that appears in search engine results.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="3" form="editPostForm"><?php echo htmlspecialchars($post['meta_description']); ?></textarea>
                        <div class="form-text">
                            <span id="metaDescCount">0</span>/160 characters. A brief description that appears in search engine results.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" form="editPostForm" value="<?php echo htmlspecialchars($post['meta_keywords']); ?>">
                        <div class="form-text">Comma-separated keywords related to the post. Example: travel, adventure, hiking</div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Search Engine Preview</h6>
                        </div>
                        <div class="card-body bg-light">
                            <div id="seoPreview" class="border p-3 rounded bg-white">
                                <div class="preview-title" style="color: #1a0dab; font-size: 18px;"></div>
                                <div class="preview-url" style="color: #006621; font-size: 14px;"></div>
                                <div class="preview-description" style="color: #545454; font-size: 14px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Publish</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" form="editPostForm">
                            <option value="draft" <?php echo $post['status'] === 'draft' ? 'selected' : ''; ?>>Draft</option>
                            <option value="published" <?php echo $post['status'] === 'published' ? 'selected' : ''; ?>>Published</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" form="editPostForm">
                            <i class="fas fa-save"></i> Update Post
                        </button>
                        <a href="posts.php" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>

            <!-- Category -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Category</h5>
                </div>
                <div class="card-body">
                    <select class="form-select" id="category_id" name="category_id" form="editPostForm" required>
                        <option value="">Select Category</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" <?php echo $post['category_id'] == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Featured Image</h5>
                </div>
                <div class="card-body">
                    <?php if ($post['featured_image']): ?>
                    <div class="current-image mb-3">
                        <img src="<?php echo SITE_URL . '/uploads/posts/' . $post['featured_image']; ?>" class="img-thumbnail" style="max-width: 100%;">
                        <div class="form-text mt-1">Current featured image</div>
                    </div>
                    <?php endif; ?>
                    <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*" form="editPostForm">
                    <div id="imagePreview" class="mt-3 text-center"></div>
                    <div class="form-text mt-2">Recommended size: 1200x630 pixels for optimal social sharing.</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quill.js Rich Text Editor -->
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">

<script>
// Global variables
let quillEditor;

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing Quill editor...');

    // Initialize Quill editor
    initializeQuillEditor();

    // Initialize Quill editor
    function initializeQuillEditor() {
        console.log('Initializing Quill editor...');

        // Check if Quill is loaded
        if (typeof Quill === 'undefined') {
            console.error('Quill not loaded, falling back to textarea');
            document.getElementById('content').style.display = 'block';
            document.getElementById('content').style.height = '400px';
            return;
        }

        // Get existing content
        const existingContent = document.getElementById('content').value;

        // Initialize Quill
        quillEditor = new Quill('#quill-editor', {
            theme: 'snow',
            placeholder: 'Start writing your travel story here...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'align': [] }],
                    ['link', 'image'],
                    ['clean']
                ]
            }
        });

        // Set existing content
        if (existingContent) {
            quillEditor.root.innerHTML = existingContent;
        }

        console.log('Quill editor initialized successfully!');
    }

    // Initialize character counters
    function updateCharCount(input, counter, limit) {
        const count = input.value.length;
        counter.textContent = count;
        counter.style.color = count > limit ? '#dc3545' : '#212529';
    }

    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    const metaTitleInput = document.getElementById('meta_title');
    const metaTitleCount = document.getElementById('metaTitleCount');
    const metaDesc = document.getElementById('meta_description');
    const metaDescCount = document.getElementById('metaDescCount');

    // Initialize character counts
    updateCharCount(metaTitleInput, metaTitleCount, 60);
    updateCharCount(metaDesc, metaDescCount, 160);

    // Title input handler
    titleInput.addEventListener('input', function(e) {
        const titleValue = e.target.value;

        // Update slug if empty or not manually edited
        if (!slugInput.value || slugInput.dataset.auto !== 'false') {
            slugInput.value = generateSlug(titleValue);
        }

        // Update meta title if empty
        if (!metaTitleInput.value) {
            metaTitleInput.value = titleValue;
            updateCharCount(metaTitleInput, metaTitleCount, 60);
        }

        // Update SEO preview
        updateSEOPreview();
    });

    // Meta title input handler
    metaTitleInput.addEventListener('input', function() {
        updateCharCount(this, metaTitleCount, 60);
        updateSEOPreview();
    });

    // Meta description input handler
    metaDesc.addEventListener('input', function() {
        updateCharCount(this, metaDescCount, 160);
        updateSEOPreview();
    });

    // Slug input handler
    slugInput.addEventListener('input', function() {
        this.dataset.auto = 'false';
        updateSEOPreview();
    });

    // Update SEO preview
    function updateSEOPreview() {
        const title = metaTitleInput.value || titleInput.value;
        const slug = slugInput.value;
        const desc = metaDesc.value || document.getElementById('excerpt').value;
        
        document.querySelector('.preview-title').textContent = title;
        document.querySelector('.preview-url').textContent = `${window.location.origin}/blog/${slug}`;
        document.querySelector('.preview-description').textContent = desc;
    }

    // Initialize SEO preview
    updateSEOPreview();

    // Add excerpt input handler for SEO preview
    document.getElementById('excerpt').addEventListener('input', updateSEOPreview);

    // Form submission
    document.getElementById('editPostForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        // Get content from Quill editor
        if (quillEditor) {
            const content = quillEditor.root.innerHTML;
            formData.set('content', content);
            console.log('Content from Quill:', content);
        } else {
            formData.set('content', document.getElementById('content').value);
            console.log('Content from textarea fallback');
        }
        
        // Add meta data
        formData.set('meta_title', metaTitleInput.value || titleInput.value);
        formData.set('meta_description', metaDesc.value || document.getElementById('excerpt').value);
        
        fetch('<?php echo ADMIN_URL; ?>/ajax/update_post.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = 'posts.php';
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the post');
        });
    });
});

// Helper function to generate slug
function generateSlug(text) {
    if (!text) return '';
    
    return text
        .toLowerCase()
        .trim()
        .normalize('NFD') // Normalize unicode characters
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Image preview with size validation
document.getElementById('featured_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = new Image();
            img.onload = function() {
                let warning = '';
                if (this.width < 1200 || this.height < 630) {
                    warning = '<div class="alert alert-warning mt-2">Image is smaller than recommended size (1200x630)</div>';
                }
                document.getElementById('imagePreview').innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 100%;">
                    <div class="text-muted mt-1">Size: ${this.width}x${this.height}</div>
                    ${warning}
                `;
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});
</script>

<?php include 'includes/footer.php'; ?> 