<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/post_functions.php';

// Check if user is logged in
requireLogin();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate required fields
    if (empty($_POST['id']) || empty($_POST['title']) || empty($_POST['category_id'])) {
        throw new Exception('Required fields are missing.');
    }

    $post_id = (int)$_POST['id'];
    
    // Get current post data
    $current_post = getPostById($post_id);
    if (!$current_post) {
        throw new Exception('Post not found.');
    }

    // Handle file upload if a new image is provided
    $featured_image = $current_post['featured_image']; // Keep existing image by default
    if (!empty($_FILES['featured_image']['name'])) {
        // Create uploads directory if it doesn't exist
        $upload_dir = '../../uploads/posts/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Process the uploaded file
        $file_info = pathinfo($_FILES['featured_image']['name']);
        $extension = strtolower($file_info['extension']);
        
        // Validate file type
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        if (!in_array($extension, $allowed_types)) {
            throw new Exception('Invalid file type. Allowed types: ' . implode(', ', $allowed_types));
        }

        // Generate unique filename
        $new_filename = uniqid() . '.' . $extension;
        $upload_path = $upload_dir . $new_filename;

        // Move uploaded file
        if (move_uploaded_file($_FILES['featured_image']['tmp_name'], $upload_path)) {
            // Delete old image if it exists
            if ($current_post['featured_image'] && file_exists($upload_dir . $current_post['featured_image'])) {
                unlink($upload_dir . $current_post['featured_image']);
            }
            $featured_image = $new_filename;
        } else {
            throw new Exception('Failed to upload image.');
        }
    }

    // Generate slug if not provided
    $slug = !empty($_POST['slug']) ? $_POST['slug'] : generateSlug($_POST['title']);

    // Prepare post data
    $post_data = [
        'id' => $post_id,
        'title' => $_POST['title'],
        'slug' => $slug,
        'content' => $_POST['content'],
        'excerpt' => $_POST['excerpt'],
        'featured_image' => $featured_image,
        'category_id' => $_POST['category_id'],
        'status' => $_POST['status'],
        'meta_title' => $_POST['meta_title'] ?: $_POST['title'],
        'meta_description' => $_POST['meta_description'] ?: $_POST['excerpt'],
        'meta_keywords' => $_POST['meta_keywords']
    ];

    // Update post in database
    $db = getDbConnection();
    $sql = "UPDATE posts SET 
            title = :title,
            slug = :slug,
            content = :content,
            excerpt = :excerpt,
            featured_image = :featured_image,
            category_id = :category_id,
            status = :status,
            meta_title = :meta_title,
            meta_description = :meta_description,
            meta_keywords = :meta_keywords,
            updated_at = NOW()
            WHERE id = :id";

    $stmt = $db->prepare($sql);
    if ($stmt->execute($post_data)) {
        $response['success'] = true;
        $response['message'] = 'Post updated successfully.';
    } else {
        throw new Exception('Failed to update post.');
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// Send JSON response
echo json_encode($response); 