<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/coupon_functions.php';
require_once 'includes/store_functions.php';
require_once 'includes/category_functions.php';

// Check if user is logged in
requireLogin();

// Get coupon ID from URL
$coupon_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get coupon data
$coupon = getCouponById($coupon_id);

// Redirect if coupon not found
if (!$coupon) {
    $_SESSION['error'] = 'Coupon not found.';
    header('Location: coupons.php');
    exit;
}

// Get stores and categories for dropdowns
$stores = getAllStores(1, 1000)['stores'];
$categories = getAllCategories();

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit Coupon</h1>
        <a href="coupons.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Coupons
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Main Coupon Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="editCouponForm">
                        <input type="hidden" name="id" value="<?php echo $coupon['id']; ?>">
                        
                        <div class="mb-4">
                            <label for="code" class="form-label">Coupon Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-lg" id="code" name="code" 
                                   value="<?php echo htmlspecialchars($coupon['code']); ?>" required>
                            <div class="form-text">Enter the unique coupon code (e.g., SUMMER2024, WELCOME10).</div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($coupon['description']); ?></textarea>
                            <div class="form-text">Provide a brief description of what this coupon offers.</div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="discount_type" class="form-label">Discount Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="discount_type" name="discount_type" required>
                                    <option value="">Select Type</option>
                                    <option value="percentage" <?php echo $coupon['discount_type'] === 'percentage' ? 'selected' : ''; ?>>Percentage</option>
                                    <option value="fixed" <?php echo $coupon['discount_type'] === 'fixed' ? 'selected' : ''; ?>>Fixed Amount</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="discount_value" class="form-label">Discount Value <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="discount_value" name="discount_value" 
                                           step="0.01" min="0" value="<?php echo $coupon['discount_value']; ?>" required>
                                    <span class="input-group-text" id="discount_symbol"><?php echo $coupon['discount_type'] === 'percentage' ? '%' : '$'; ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="store_id" class="form-label">Store <span class="text-danger">*</span></label>
                                <select class="form-select" id="store_id" name="store_id" required>
                                    <option value="">Select Store</option>
                                    <?php foreach ($stores as $store): ?>
                                        <option value="<?php echo $store['id']; ?>" <?php echo $store['id'] == $coupon['store_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($store['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo $category['id'] == $coupon['category_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="affiliate_link" class="form-label">Affiliate Link</label>
                            <input type="url" class="form-control" id="affiliate_link" name="affiliate_link" 
                                   value="<?php echo htmlspecialchars($coupon['affiliate_link'] ?? ''); ?>"
                                   placeholder="https://example.com/affiliate-link">
                            <div class="form-text">Your affiliate link for this coupon (optional).</div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="<?php echo $coupon['start_date'] ? date('Y-m-d', strtotime($coupon['start_date'])) : ''; ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" 
                                       value="<?php echo $coupon['end_date'] ? date('Y-m-d', strtotime($coupon['end_date'])) : ''; ?>">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Publish</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" form="editCouponForm">
                            <option value="active" <?php echo $coupon['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $coupon['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" form="editCouponForm">
                            <i class="fas fa-save"></i> Update Coupon
                        </button>
                        <a href="coupons.php" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountType = document.getElementById('discount_type');
    const discountValue = document.getElementById('discount_value');
    const discountSymbol = document.getElementById('discount_symbol');
    const endDate = document.getElementById('end_date');
    const startDate = document.getElementById('start_date');

    // Update discount symbol based on type
    discountType.addEventListener('change', function() {
        discountSymbol.textContent = this.value === 'percentage' ? '%' : '$';
        if (this.value === 'percentage') {
            discountValue.max = 100;
        } else {
            discountValue.max = '';
        }
    });

    // Set minimum end date based on start date
    startDate.addEventListener('change', function() {
        if (this.value) {
            endDate.min = this.value;
            if (endDate.value && endDate.value < this.value) {
                endDate.value = this.value;
            }
        } else {
            endDate.min = '';
        }
    });

    // Form submission
    document.getElementById('editCouponForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);

        // Submit the form
        fetch('ajax/update_coupon.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = 'coupons.php';
            } else {
                alert(data.message || 'Error updating coupon');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the coupon');
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?> 