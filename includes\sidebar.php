<?php
require_once 'config.php';
require_once 'db_queries.php';

// Get popular posts with all necessary details
function getPopularPostsWithDetails($limit = 2) {
    global $conn;
    try {
        // Simpler query first to debug
        $sql = "SELECT p.id, p.title, p.slug, p.created_at 
                FROM posts p 
                WHERE p.status = 'published' 
                ORDER BY p.views DESC 
                LIMIT ?";
                
        $stmt = $conn->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching popular posts: " . $e->getMessage());
        return [];
    }
}

// Get categories with post count and icons
function getCategoriesWithPostCount() {
    global $conn;
    $sql = "SELECT c.*, COUNT(p.id) as post_count 
            FROM categories c 
            LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published'
            WHERE c.status = 'active' 
            GROUP BY c.id 
            HAVING post_count > 0 
            ORDER BY post_count DESC";
            
    try {
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching categories: " . $e->getMessage());
        return [];
    }
}

// Fetch actual data from database using existing functions
$popular_posts = getPopularPosts(2); // Using the existing function from db_queries.php
$categories = getCategoriesWithCount(); // Using the existing function from db_queries.php

// Debug information
if (empty($popular_posts)) {
    error_log("No posts found in sidebar");
}
?>

<aside class="sidebar">
    <!-- Autres Magasins Title -->
    <div class="sidebar-header">
        <h2 class="sidebar-title">Autres Magasins</h2>
    </div>

    <!-- Popular Articles Section -->
    <div class="sidebar-widget">
        <h3 class="widget-title">ARTICLES POPULAIRES</h3>
        <div class="popular-articles">
            <?php if (!empty($popular_posts)): ?>
                <?php foreach ($popular_posts as $post): ?>
                <article class="article-item">
                    <a href="post.php?slug=<?php echo htmlspecialchars($post['slug']); ?>" class="article-link">
                        <div class="article-content">
                            <h4 class="article-title"><?php echo htmlspecialchars($post['title']); ?></h4>
                            <div class="article-meta">
                                <span class="article-date">
                                    <i class="far fa-calendar"></i>
                                    <?php echo formatDate($post['created_at']); ?>
                                </span>
                            </div>
                        </div>
                    </a>
                </article>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="no-posts">Aucun article disponible</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Categories Section -->
    <div class="sidebar-widget">
        <h3 class="widget-title">CATÉGORIES</h3>
        <ul class="categories-list">
            <?php foreach ($categories as $category): ?>
            <li class="category-item">
                <a href="category.php?slug=<?php echo htmlspecialchars($category['slug']); ?>" class="category-link">
                    <span class="category-name">
                        <i class="<?php echo htmlspecialchars($category['icon'] ?? 'fas fa-folder'); ?>"></i>
                        <?php echo htmlspecialchars($category['name']); ?>
                    </span>
                    <span class="category-count">(<?php echo $category['post_count']; ?>)</span>
                </a>
            </li>
            <?php endforeach; ?>
        </ul>
    </div>
</aside>

<style>
.sidebar {
    width: 100%;
    max-width: 300px;
    margin-left: 2rem;
}

.sidebar-header {
    margin-bottom: 2rem;
}

.sidebar-title {
    font-size: 1.75rem;
    color: #333;
    margin: 0;
    font-weight: 600;
}

.widget-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #eee;
}

.popular-articles {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.article-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.article-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.article-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.article-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.article-title {
    font-size: 0.95rem;
    color: #333;
    margin: 0;
    line-height: 1.4;
    font-weight: 500;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.813rem;
    color: #666;
}

.article-date {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.article-date i {
    font-size: 0.875rem;
    color: #999;
}

.categories-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    margin-bottom: 0.75rem;
}

.category-item:last-child {
    margin-bottom: 0;
}

.category-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-decoration: none;
    color: #333;
    font-size: 0.938rem;
    padding: 0.5rem 0;
    transition: color 0.2s ease;
}

.category-link:hover {
    color: #0066cc;
}

.category-count {
    color: #666;
    font-size: 0.875rem;
}

.sidebar-widget {
    margin-bottom: 2.5rem;
    padding-bottom: 2.5rem;
    border-bottom: 1px solid #eee;
}

.sidebar-widget:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.no-posts {
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        max-width: 100%;
        margin-left: 0;
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .sidebar-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .article-title {
        font-size: 0.9rem;
    }
}
</style> 