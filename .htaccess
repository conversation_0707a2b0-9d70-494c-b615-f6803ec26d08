RewriteEngine On

# Always serve existing files and directories as-is
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Store details: /store/store-slug -> store.php?slug=store-slug
RewriteRule ^store/([a-zA-Z0-9_-]+)/?$ store.php?slug=$1 [L,QSA]

# Coupon details: /coupon/123 -> coupon.php?id=123
RewriteRule ^coupon/([0-9]+)/?$ coupon.php?id=$1 [L,QSA]

# Category details: /category/slug -> category.php?slug=slug
RewriteRule ^category/([a-zA-Z0-9_-]+)/?$ category.php?slug=$1 [L,QSA]

# Post details: /post/slug -> post.php?slug=slug
RewriteRule ^post/([a-zA-Z0-9_-]+)/?$ post.php?slug=$1 [L,QSA]

# Dynamic pages: /page/slug -> page.php?slug=slug
RewriteRule ^page/([a-zA-Z0-9_-]+)/?$ page.php?slug=$1 [L,QSA]

# Coupons page: /coupons -> coupons.php
RewriteRule ^coupons/?$ coupons.php [L,QSA]

# Search page: /search -> search.php
RewriteRule ^search/?$ search.php [L,QSA]

# Home page
DirectoryIndex index.php
