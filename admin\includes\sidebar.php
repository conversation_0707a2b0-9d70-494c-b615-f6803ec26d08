<?php
require_once 'category_functions.php';

// Get categories for sidebar
$sidebarCategories = getActiveCategoriesForSidebar();
?>
<nav id="sidebar">
    <div class="sidebar-header">
        <h3><?php echo SITE_NAME; ?></h3>
    </div>
    
    <ul class="list-unstyled components">
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/index.php">
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
            </a>
        </li>
        
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'categories.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/categories.php">
                <i class="fas fa-folder"></i>
                <span>Categories</span>
            </a>
        </li>
        
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'posts.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/posts.php">
                <i class="fas fa-file-alt"></i>
                <span>Blog Posts</span>
            </a>
        </li>
        
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'stores.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/stores.php">
                <i class="fas fa-store"></i>
                <span>Stores</span>
            </a>
        </li>
        
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'coupons.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/coupons.php">
                <i class="fas fa-tags"></i>
                <span>Coupons</span>
            </a>
        </li>
        
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/users.php">
                <i class="fas fa-users"></i>
                <span>Users</span>
            </a>
        </li>
        
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'comments.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/comments.php">
                <i class="fas fa-comments"></i>
                <span>Comments</span>
            </a>
        </li>
        
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'pages.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/pages.php">
                <i class="fas fa-file-alt"></i>
                <span>Pages</span>
            </a>
        </li>
        
        <li>
            <a href="http://wa.me/923059052727" target="_blank">
                <i class="fab fa-whatsapp"></i>
                <span>Support</span>
            </a>
        </li>
        
        <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
            <a href="<?php echo ADMIN_URL; ?>/settings.php">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </li>
    </ul>
</nav>

<style>
/* Sidebar Styles */
#sidebar {
    font-size: 0.9rem;
}

#sidebar .components {
    padding: 0;
}

#sidebar ul li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

#sidebar ul li:last-child {
    border-bottom: none;
}

#sidebar ul li a {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

#sidebar ul li a:hover {
    color: #fff;
    background: var(--secondary-color);
    border-left-color: var(--accent-color);
}

#sidebar ul li.active > a {
    color: #fff;
    background: var(--accent-color);
    border-left-color: #fff;
}

#sidebar ul li a i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

#sidebar ul li a span {
    flex: 1;
}

/* Responsive Sidebar */
@media (max-width: 767.98px) {
    #sidebar {
        margin-left: -250px;
    }
    
    #sidebar.active {
        margin-left: 0;
    }
    
    #sidebar ul li a span {
        display: inline;
    }
}

@media (max-width: 575.98px) {
    #sidebar {
        font-size: 0.85rem;
    }
    
    #sidebar ul li a {
        padding: 10px 15px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add active class to current page link
    const currentPath = window.location.pathname;
    const links = document.querySelectorAll('#sidebar a');
    
    links.forEach(link => {
        if (link.getAttribute('href').includes(currentPath)) {
            link.closest('li').classList.add('active');
        }
    });
});
</script>