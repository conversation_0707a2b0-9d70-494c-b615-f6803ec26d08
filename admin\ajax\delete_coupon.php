<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/coupon_functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate input
    if (empty($_POST['id'])) {
        throw new Exception('Coupon ID is required.');
    }

    $coupon_id = (int)$_POST['id'];

    // Check if coupon exists
    $coupon = getCouponById($coupon_id);
    if (!$coupon) {
        throw new Exception('Coupon not found.');
    }

    // Delete the coupon
    if (deleteCoupon($coupon_id)) {
        $response['success'] = true;
        $response['message'] = 'Coupon deleted successfully.';
    } else {
        throw new Exception('Failed to delete coupon.');
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 