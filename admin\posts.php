<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/post_functions.php';
require_once 'includes/category_functions.php';

// Check if user is logged in
requireLogin();

// Get current page number and items per page
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 10;

// Get posts with pagination
$result = getAllPosts($page, $per_page);
$posts = $result['posts'];
$total_posts = $result['total'];

// Calculate total pages (ensure we don't divide by zero)
$total_pages = $per_page > 0 ? ceil($total_posts / $per_page) : 1;

// Ensure current page is within valid range
$page = min($page, max(1, $total_pages));

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Blog Posts</h1>
        <a href="add_post.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Post
        </a>
    </div>

    <?php if (!empty($posts)): ?>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Author</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($posts as $post): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if (!empty($post['featured_image'])): ?>
                                    <img src="<?php echo SITE_URL; ?>/uploads/posts/<?php echo htmlspecialchars($post['featured_image']); ?>" 
                                         class="img-thumbnail me-2" 
                                         style="width: 50px; height: 50px; object-fit: cover;" 
                                         alt="<?php echo htmlspecialchars($post['title']); ?>">
                                    <?php endif; ?>
                                    <div>
                                        <strong><?php echo htmlspecialchars($post['title']); ?></strong>
                                        <?php if (!empty($post['excerpt'])): ?>
                                        <div class="small text-muted"><?php echo htmlspecialchars(substr($post['excerpt'], 0, 100)) . '...'; ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($post['category_name']); ?></td>
                            <td><?php echo htmlspecialchars($post['author_name']); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $post['status'] === 'published' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst(htmlspecialchars($post['status'])); ?>
                                </span>
                            </td>
                            <td><?php echo date('M j, Y', strtotime($post['created_at'])); ?></td>
                            <td>
                                <div class="btn-group">
                                    <a href="edit_post.php?id=<?php echo $post['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" onclick="deletePost(<?php echo $post['id']; ?>)">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo ($page - 1); ?>">Previous</a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo ($page + 1); ?>">Next</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
    <?php else: ?>
    <div class="alert alert-info">
        No posts found. <a href="add_post.php">Create your first post</a>
    </div>
    <?php endif; ?>
</div>

<script>
function deletePost(postId) {
    if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
        fetch('ajax/delete_post.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: postId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.message || 'Error deleting post');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the post');
        });
    }
}
</script>

<?php include 'includes/footer.php'; ?> 