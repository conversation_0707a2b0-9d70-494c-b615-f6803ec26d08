<?php
// Ensure session is started AT THE VERY TOP before any output or session access
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once 'includes/config.php';

// Redirect if already logged in (isLoggedIn function relies on session)
if (isLoggedIn()) {
    header("Location: index.php");
    exit();
}

$error = '';

// --- Final CAPTCHA Generation Logic ---
// Generate a new CAPTCHA only if one doesn't exist in the session (e.g., first visit)
if (!isset($_SESSION['captcha_question'])) {
    $captcha_num1 = rand(1, 9);
    $captcha_num2 = rand(1, 9);
    $_SESSION['captcha_result'] = $captcha_num1 + $captcha_num2;
    $_SESSION['captcha_question'] = "What is {$captcha_num1} + {$captcha_num2}?";
}
// Always use the question stored in the session for display
$captcha_question = $_SESSION['captcha_question'];
// --- End Final CAPTCHA Generation Logic ---


if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $captcha_input = isset($_POST['captcha']) ? (int)$_POST['captcha'] : null;

    // --- Final CAPTCHA Verification Logic ---
    // Check against the result stored in the session using non-strict comparison
    if (!isset($_SESSION['captcha_result']) || $captcha_input === null || $captcha_input != $_SESSION['captcha_result']) {
        $error = 'Incorrect CAPTCHA answer.';
        // DO NOT regenerate captcha here. Let the user retry the same question.
    } else {
        // CAPTCHA was correct, proceed to check credentials
        try {
            $stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Credentials correct! Set user session variables
                $_SESSION['admin_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['admin_role'] = $user['role'];

                // Unset CAPTCHA variables ONLY after successful login
                unset($_SESSION['captcha_result']);
                unset($_SESSION['captcha_question']);

                header("Location: index.php");
                exit();
            } else {
                // Credentials incorrect (but CAPTCHA was correct)
                $error = 'Invalid username or password';
                // DO NOT regenerate captcha here. Let the user retry the same question.
            }
        } catch(PDOException $e) {
            $error = 'An error occurred during login. Please try again.';
            // DO NOT regenerate captcha here.
        }
    }
    // --- End Final CAPTCHA Verification Logic ---
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Keep external CSS link in case it works for other styles -->
    <link href="assets/css/admin.css?v=<?php echo time(); ?>" rel="stylesheet"> 
    <!-- Embed only the background style as a fallback -->
    <style>
        body { 
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%) !important; /* Use !important to force override */
            /* Add padding to prevent content touching edges on small screens */
            padding-top: 3rem; 
            padding-bottom: 3rem;
        }
        /* Ensure card is vertically centered if body takes full height */
         html, body {
             height: 100%;
         }
         body {
             display: flex;
             align-items: center; /* Vertical center */
         }
    </style>
</head>
<body> <!-- Removed login-body class -->
    <div class="container mt-5"> <!-- Use standard container for basic layout -->
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <!-- Replaced image with Site Name text -->
                            <h2 class="h3 mb-3 fw-bold"><?php echo SITE_NAME; ?></h2> 
                            <h1 class="h4 mb-1">Welcome Back</h1> <!-- Adjusted heading size -->
                            <p class="text-muted small">Please login to your account</p> <!-- Adjusted text size -->
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <!-- Standard Input (Icon Removed) -->
                            <div class="mb-3">
                                <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                            </div>
                            
                             <!-- Standard Input (Icon Removed) -->
                            <div class="mb-3">
                                 <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                            </div>
                            
                            <!-- Standard Input for Captcha -->
                            <div class="mb-3">
                                 <input type="number" class="form-control" id="captcha" name="captcha" placeholder="<?php echo $captcha_question; ?>" required>
                            </div>

                            <button type="submit" class="btn btn-primary w-100"> <!-- Use standard btn-primary -->
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </button>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="forgot-password.php">Forgot your password?</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
