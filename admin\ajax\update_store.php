<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/store_functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate required fields
    if (empty($_POST['id']) || empty($_POST['name']) || empty($_POST['slug']) || empty($_POST['website_url'])) {
        throw new Exception('Store ID, name, slug, and website URL are required.');
    }

    // Validate store data
    $validation = validateStoreData($_POST);
    if (!$validation['valid']) {
        throw new Exception(implode("\n", $validation['errors']));
    }

    // Get current store data
    $store = getStoreById($_POST['id']);
    if (!$store) {
        throw new Exception('Store not found.');
    }

    // Handle file upload if a new logo is provided
    $logo_filename = $_POST['current_logo'] ?? '';
    if (!empty($_FILES['logo']['name'])) {
        // Create uploads directory if it doesn't exist
        $upload_dir = '../../uploads/stores/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Process the uploaded file
        $file_info = pathinfo($_FILES['logo']['name']);
        $extension = strtolower($file_info['extension']);
        
        // Validate file type
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        if (!in_array($extension, $allowed_types)) {
            throw new Exception('Invalid file type. Allowed types: ' . implode(', ', $allowed_types));
        }

        // Validate file size (2MB max)
        if ($_FILES['logo']['size'] > 2 * 1024 * 1024) {
            throw new Exception('File size must be less than 2MB.');
        }

        // Generate unique filename
        $new_logo_filename = uniqid() . '.' . $extension;
        $upload_path = $upload_dir . $new_logo_filename;

        // Move uploaded file
        if (!move_uploaded_file($_FILES['logo']['tmp_name'], $upload_path)) {
            throw new Exception('Failed to upload logo.');
        }

        // Delete old logo if it exists
        if (!empty($store['logo'])) {
            $old_logo_path = $upload_dir . $store['logo'];
            if (file_exists($old_logo_path)) {
                unlink($old_logo_path);
            }
        }

        $logo_filename = $new_logo_filename;
    }

    // Prepare store data
    $store_data = [
        'id' => (int)$_POST['id'],
        'name' => trim($_POST['name']),
        'slug' => trim($_POST['slug']),
        'description' => trim($_POST['description'] ?? ''),
        'logo' => $logo_filename,
        'website_url' => trim($_POST['website_url']),
        'affiliate_id' => trim($_POST['affiliate_id'] ?? ''),
        'status' => $_POST['status'] ?? 'inactive'
    ];

    // Begin transaction
    $conn->beginTransaction();

    // Update store
    $sql = "UPDATE stores SET 
                name = :name,
                slug = :slug,
                description = :description,
                logo = :logo,
                website_url = :website_url,
                affiliate_id = :affiliate_id,
                status = :status
            WHERE id = :id";

    $stmt = $conn->prepare($sql);
    
    if ($stmt->execute($store_data)) {
        // Commit transaction
        $conn->commit();
        
        $response['success'] = true;
        $response['message'] = 'Store updated successfully.';
    } else {
        throw new Exception('Failed to update store.');
    }

} catch (Exception $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // Delete newly uploaded logo if exists
    if (!empty($new_logo_filename)) {
        $logo_path = '../../uploads/stores/' . $new_logo_filename;
        if (file_exists($logo_path)) {
            unlink($logo_path);
        }
    }
    
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 