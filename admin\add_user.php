<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
requireLogin();

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Add New User</h1>
        <a href="users.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Main User Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="addUserForm" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="form-text">Choose a unique username.</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="form-text">Enter a valid email address.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">Password must be at least 8 characters long.</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <div class="form-text">Re-enter the password to confirm.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Select a role</option>
                                <option value="admin">Admin</option>
                                <option value="editor">Editor</option>
                                <option value="user">User</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="avatar" class="form-label">Avatar</label>
                            <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*">
                            <div class="form-text">Upload a profile picture (optional).</div>
                            <div id="avatarPreview" class="mt-2"></div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Help Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Help
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Required Fields</h6>
                    <p>Fields marked with <span class="text-danger">*</span> are required.</p>
                    
                    <h6>Password Requirements</h6>
                    <ul class="mb-0">
                        <li>Minimum 8 characters</li>
                        <li>Should include numbers and letters</li>
                        <li>Case sensitive</li>
                    </ul>
                    
                    <h6 class="mt-3">Avatar Guidelines</h6>
                    <ul class="mb-0">
                        <li>Maximum file size: 5MB</li>
                        <li>Supported formats: JPG, PNG, GIF</li>
                        <li>Recommended size: 200x200 pixels</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Avatar preview
    $('#avatar').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#avatarPreview').html(`
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">
                `);
            }
            reader.readAsDataURL(file);
        }
    });

    // Form validation and submission
    $('#addUserForm').submit(function(e) {
        e.preventDefault();
        
        // Basic validation
        if ($('#password').val() !== $('#confirm_password').val()) {
            alert('Passwords do not match.');
            return;
        }

        if ($('#password').val().length < 8) {
            alert('Password must be at least 8 characters long.');
            return;
        }

        // Create FormData object
        const formData = new FormData(this);

        // Submit form via AJAX
        $.ajax({
            url: 'ajax/add_user.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    window.location.href = 'users.php';
                } else {
                    alert(response.message || 'Failed to add user.');
                }
            },
            error: function() {
                alert('An error occurred while adding the user.');
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?> 