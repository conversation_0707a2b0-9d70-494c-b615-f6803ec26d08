<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in
requireLogin();

// Get user ID from URL
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Fetch user data
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Redirect if user not found
if (!$user) {
    $_SESSION['error'] = 'User not found.';
    header('Location: users.php');
    exit();
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit User</h1>
        <a href="users.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Main User Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="editUserForm" enctype="multipart/form-data">
                        <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                <div class="form-text">Choose a unique username.</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                <div class="form-text">Enter a valid email address.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="password" name="password">
                                <div class="form-text">Leave blank to keep current password.</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?php echo htmlspecialchars($user['first_name']); ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?php echo htmlspecialchars($user['last_name']); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Select a role</option>
                                <option value="admin" <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                <option value="editor" <?php echo $user['role'] === 'editor' ? 'selected' : ''; ?>>Editor</option>
                                <option value="user" <?php echo $user['role'] === 'user' ? 'selected' : ''; ?>>User</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="avatar" class="form-label">Avatar</label>
                            <?php if (!empty($user['avatar'])): ?>
                                <div class="mb-2">
                                    <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="Current Avatar" 
                                         class="img-thumbnail" style="max-width: 200px;">
                                </div>
                            <?php endif; ?>
                            <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*">
                            <div class="form-text">Upload a new profile picture (optional).</div>
                            <div id="avatarPreview" class="mt-2"></div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Help Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Help
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Required Fields</h6>
                    <p>Fields marked with <span class="text-danger">*</span> are required.</p>
                    
                    <h6>Password Requirements</h6>
                    <ul class="mb-0">
                        <li>Minimum 8 characters</li>
                        <li>Should include numbers and letters</li>
                        <li>Case sensitive</li>
                    </ul>
                    
                    <h6 class="mt-3">Avatar Guidelines</h6>
                    <ul class="mb-0">
                        <li>Maximum file size: 5MB</li>
                        <li>Supported formats: JPG, PNG, GIF</li>
                        <li>Recommended size: 200x200 pixels</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Avatar preview
    $('#avatar').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#avatarPreview').html(`
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">
                `);
            }
            reader.readAsDataURL(file);
        }
    });

    // Form validation and submission
    $('#editUserForm').submit(function(e) {
        e.preventDefault();
        
        // Password validation if new password is provided
        const password = $('#password').val();
        if (password) {
            if (password !== $('#confirm_password').val()) {
                alert('Passwords do not match.');
                return;
            }

            if (password.length < 8) {
                alert('Password must be at least 8 characters long.');
                return;
            }
        }

        // Create FormData object
        const formData = new FormData(this);

        // Submit form via AJAX
        $.ajax({
            url: 'ajax/update_user.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    window.location.href = 'users.php';
                } else {
                    alert(response.message || 'Failed to update user.');
                }
            },
            error: function() {
                alert('An error occurred while updating the user.');
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?> 