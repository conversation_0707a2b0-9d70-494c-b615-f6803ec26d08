<?php
require_once 'includes/config.php';
require_once 'includes/db_queries.php';

// Get selected category if any
$selected_category = isset($_GET['category']) ? $_GET['category'] : '';

// Get stores and categories
if (!empty($selected_category)) {
    $stores = getStoresByCategory($selected_category);
} else {
    $stores = getAllStoresWithCoupons();
}
$categories = getAllCategories();

include 'includes/header.php';
?>

<div class="container">
    <div class="main-content">
        <div class="content-area">
            <div class="page-header">
                <h1 class="page-title">Nos Boutiques Partenaires</h1>
                <p class="page-description">Découvrez toutes les boutiques partenaires et accédez à leurs coupons exclusifs.</p>
                <!-- Category Filter -->
                <div class="category-filter">
                    <a href="/coupons" class="category-btn <?php echo empty($selected_category) ? 'active' : ''; ?>">
                        <i class="fas fa-th-large"></i>
                        Toutes les boutiques
                    </a>
                    <?php foreach ($categories as $category): ?>
                    <a href="/coupons?category=<?php echo $category['slug']; ?>" 
                       class="category-btn <?php echo $selected_category === $category['slug'] ? 'active' : ''; ?>">
                        <i class="<?php echo !empty($category['icon']) ? $category['icon'] : 'fas fa-tag'; ?>"></i>
                        <?php echo htmlspecialchars($category['name']); ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <!-- Stores Section -->
            <section class="stores-section">
                <?php if (!empty($stores)): ?>
                    <div class="stores-list">
                        <?php foreach ($stores as $store): ?>
                        <div class="store-article">
                            <div class="store-header">
                                <div class="store-info">
                                    <?php if (!empty($store['logo'])): ?>
                                    <img src="uploads/stores/<?php echo htmlspecialchars($store['logo']); ?>" 
                                         alt="<?php echo htmlspecialchars($store['name']); ?>" 
                                         class="store-logo">
                                    <?php endif; ?>
                                    <div class="store-details">
                                        <h3>
                                            <a href="/store/<?php echo urlencode($store['slug']); ?>" style="color:inherit;text-decoration:none;">
                                                <?php echo htmlspecialchars($store['name']); ?>
                                            </a>
                                        </h3>
                                        <p class="store-description"><?php echo htmlspecialchars($store['description'] ?? ''); ?></p>
                                        <div class="store-stats">
                                            <span class="coupon-count">
                                                <i class="fas fa-ticket-alt"></i> 
                                                <?php echo isset($store['active_coupons']) ? $store['active_coupons'] : (isset($store['total_coupons']) ? $store['total_coupons'] : 0); ?> coupons actifs
                                            </span>
                                            <?php if (!empty($store['website_url'])): ?>
                                            <a href="<?php echo htmlspecialchars($store['website_url']); ?>" 
                                               target="_blank" 
                                               class="store-website">
                                                <i class="fas fa-external-link-alt"></i> 
                                                Visiter le site
                                            </a>
                                            <?php endif; ?>
                                            <a href="/store/<?php echo urlencode($store['slug']); ?>" class="btn-primary" style="margin-left:1rem;">
                                                <i class="fas fa-tags"></i> Voir les coupons
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <h3>Aucune boutique trouvée</h3>
                        <p>Aucune boutique n'est disponible pour cette catégorie pour le moment.</p>
                        <a href="/coupons" class="btn-primary">Voir toutes les boutiques</a>
                    </div>
                <?php endif; ?>
            </section>
        </div>
        <?php include 'includes/sidebar.php'; ?>
    </div>
</div>

<style>
:root {
    --primary-color: #0A0A4A;
    --secondary-color: #FF7F00;
    --light-bg: #f5f5f5;
    --text-color: #333;
    --light-text: #777;
    --border-color: #ddd;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--primary-color);
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 15px rgba(10, 10, 74, 0.1);
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: white;
}

.page-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
}

/* Category Filter */
.category-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    margin-top: 2rem;
}

.category-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 500;
}

.category-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    border-color: var(--secondary-color);
}

.category-btn.active {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

.category-btn i {
    color: var(--secondary-color);
}

.category-btn:hover i,
.category-btn.active i {
    color: white;
}

/* Store Articles */
.store-article {
    margin-bottom: 3rem;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(10, 10, 74, 0.1);
}

.store-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a7a 100%);
    padding: 2rem;
    color: white;
}

.store-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.store-logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.store-details h3 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
    color: white;
}

.store-description {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
    font-size: 1rem;
    line-height: 1.6;
}

.store-stats {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.coupon-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.store-stats i {
    color: var(--secondary-color);
}

.store-website {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--secondary-color);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.store-website:hover {
    background: white;
    color: var(--primary-color);
}

.store-website:hover i {
    color: var(--primary-color);
}

/* Coupon Articles */
.coupons-list {
    padding: 2rem;
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.coupon-article {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.coupon-article:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(10, 10, 74, 0.1);
    border-color: var(--secondary-color);
}

.coupon-article-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.coupon-meta {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.discount-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: bold;
    font-size: 1.1rem;
    color: white;
}

.discount-badge.percentage {
    background: var(--primary-color);
}

.discount-badge.fixed {
    background: var(--secondary-color);
}

.category-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--light-bg);
    color: var(--text-color);
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
}

.category-badge i {
    color: var(--secondary-color);
}

.coupon-date {
    color: var(--light-text);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.coupon-date i {
    color: var(--secondary-color);
}

.coupon-article-content {
    flex-grow: 1;
}

.coupon-article-content h3 {
    font-size: 1.2rem;
    color: var(--text-color);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.coupon-details {
    display: grid;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.coupon-detail {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--light-text);
    font-size: 0.9rem;
}

.coupon-detail i {
    color: var(--primary-color);
}

.coupon-article-footer {
    margin-top: auto;
    text-align: center;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    width: 100%;
    justify-content: center;
}

.btn-primary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(10, 10, 74, 0.1);
}

.no-results i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.no-results h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.no-results p {
    color: var(--light-text);
    margin-bottom: 2rem;
}

.no-results .btn-primary {
    max-width: 250px;
    margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 992px) {
    .store-info {
        flex-direction: column;
        text-align: center;
    }
    
    .store-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .store-website {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .category-filter {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 0.5rem;
        margin: 1rem -1rem;
        -webkit-overflow-scrolling: touch;
    }
    
    .category-btn {
        white-space: nowrap;
    }
    
    .coupons-list {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.75rem;
    }
    
    .store-logo {
        width: 80px;
        height: 80px;
    }
    
    .store-details h3 {
        font-size: 1.5rem;
    }
    
    .coupon-article {
        padding: 1rem;
    }
    
    .coupon-article-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .coupon-meta {
        align-items: center;
    }
    
    .coupon-date {
        width: 100%;
        justify-content: center;
    }
}
</style>

<?php include 'includes/footer.php'; ?>