<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate comment ID
    if (empty($_POST['id'])) {
        throw new Exception('Comment ID is required.');
    }

    $comment_id = (int)$_POST['id'];
    if ($comment_id <= 0) {
        throw new Exception('Invalid comment ID.');
    }

    // Check if comment exists
    $stmt = $conn->prepare("SELECT * FROM comments WHERE id = ?");
    $stmt->execute([$comment_id]);
    if (!$stmt->fetch()) {
        throw new Exception('Comment not found.');
    }

    // Begin transaction
    $conn->beginTransaction();

    // Delete all replies to this comment first
    $stmt = $conn->prepare("DELETE FROM comments WHERE parent_id = ?");
    $stmt->execute([$comment_id]);

    // Delete the comment itself
    $stmt = $conn->prepare("DELETE FROM comments WHERE id = ?");
    if ($stmt->execute([$comment_id])) {
        $conn->commit();
        $response['success'] = true;
        $response['message'] = 'Comment and its replies deleted successfully.';
    } else {
        throw new Exception('Failed to delete comment.');
    }

} catch (Exception $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 