<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/store_functions.php';

// Check if user is logged in
requireLogin();

// Get current page number and items per page
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 10;

// Get stores with pagination
$result = getAllStores($page, $per_page);
$stores = $result['stores'];
$total_stores = $result['total'];

// Calculate total pages (ensure we don't divide by zero)
$total_pages = $per_page > 0 ? ceil($total_stores / $per_page) : 1;

// Ensure current page is within valid range
$page = min($page, max(1, $total_pages));

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Stores</h1>
        <a href="add_store.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Store
        </a>
    </div>

    <?php if (!empty($stores)): ?>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Store</th>
                            <th>Website</th>
                            <th>Coupons</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($stores as $store): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if (!empty($store['logo'])): ?>
                                    <img src="<?php echo SITE_URL; ?>/uploads/stores/<?php echo htmlspecialchars($store['logo']); ?>" 
                                         class="img-thumbnail me-2" 
                                         style="width: 50px; height: 50px; object-fit: cover;" 
                                         alt="<?php echo htmlspecialchars($store['name']); ?>">
                                    <?php endif; ?>
                                    <div>
                                        <strong><?php echo htmlspecialchars($store['name']); ?></strong>
                                        <?php if (!empty($store['description'])): ?>
                                        <div class="small text-muted"><?php echo htmlspecialchars(substr($store['description'], 0, 100)) . '...'; ?></div>
                                        <?php endif; ?>
                                        <div class="small text-muted">Slug: <?php echo htmlspecialchars($store['slug']); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <a href="<?php echo htmlspecialchars($store['website_url']); ?>" target="_blank" class="text-decoration-none">
                                    <i class="fas fa-external-link-alt me-1"></i> Visit Website
                                </a>
                                <?php if (!empty($store['affiliate_id'])): ?>
                                <div class="small text-muted">Affiliate ID: <?php echo htmlspecialchars($store['affiliate_id']); ?></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    <?php echo $store['coupon_count']; ?> Coupons
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $store['status'] === 'active' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst(htmlspecialchars($store['status'])); ?>
                                </span>
                            </td>
                            <td><?php echo date('M j, Y', strtotime($store['created_at'])); ?></td>
                            <td>
                                <div class="btn-group">
                                    <a href="edit_store.php?id=<?php echo $store['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" onclick="deleteStore(<?php echo $store['id']; ?>)">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo ($page - 1); ?>">Previous</a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo ($page + 1); ?>">Next</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
    <?php else: ?>
    <div class="alert alert-info">
        No stores found. <a href="add_store.php">Add your first store</a>
    </div>
    <?php endif; ?>
</div>

<script>
function deleteStore(storeId) {
    if (confirm('Are you sure you want to delete this store? This will also delete all coupons associated with this store. This action cannot be undone.')) {
        fetch('ajax/delete_store.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + storeId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.message || 'Error deleting store');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the store');
        });
    }
}
</script>

<?php include 'includes/footer.php'; ?> 