<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trip Nest - Votre Compagnon de Voyage</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        .header-banner {
            width: 100%;
            background-image: url('assets/images/header-bg.jpeg');
            background-size: 100% 150%;
            background-position: center 15%;
            position: relative;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 0 20px;
            overflow: hidden;
        }

        .logo-container {
            display: flex;
            align-items: center;
            margin-left: 20px;
        }

        .logo-text {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            text-align: left;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .logo-text span {
            color: #FF7F00;
        }

        .logo-image {
            display: none;
        }

        .mascot-image {
            display: none;
        }

        .orange-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 30px;
            background-color: rgba(255, 127, 0, 0.7);
            clip-path: path('M0,15 Q300,0 600,15 T1200,15 T1800,15 V30 H0 Z');
            z-index: 1;
        }

        .navigation {
            background-color: #0A0A4A;
            display: flex;
            justify-content: space-between;
            padding: 10px 20px;
            color: white;
        }

        .nav-links {
            display: flex;
            align-items: center;
        }

        .nav-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
            text-decoration: none;
            color: white;
            font-weight: bold;
            transition: color 0.3s;
        }

        .nav-item:hover {
            color: #FF7F00;
        }

        .nav-item i {
            margin-right: 8px;
            color: #FF7F00;
        }

        .search-container {
            display: flex;
            align-items: center;
        }

        .search-input {
            padding: 8px 15px;
            border: none;
            border-radius: 20px 0 0 20px;
            outline: none;
        }

        .search-button {
            background-color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 0 20px 20px 0;
            cursor: pointer;
        }

        .search-button i {
            color: #0A0A4A;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        @media screen and (max-width: 992px) {
            .nav-links {
                display: none;
                flex-direction: column;
                position: absolute;
                top: 170px;
                left: 0;
                width: 100%;
                background-color: #0A0A4A;
                z-index: 10;
                padding: 20px;
            }

            .nav-links.active {
                display: flex;
            }

            .nav-item {
                margin: 10px 0;
            }

            .mobile-menu-btn {
                display: block;
            }

            .search-container {
                margin-left: auto;
            }
        }

        @media screen and (max-width: 768px) {
            .header-banner {
                height: 90px;
            }

            .logo-text {
                font-size: 1.5rem;
            }

            .search-input {
                width: 120px;
            }
        }

        @media screen and (max-width: 576px) {
            .header-banner {
                height: 70px;
            }

            .logo-text {
                font-size: 1.2rem;
            }

            .search-container {
                display: none;
            }
        }

        .nav-item.active {
            color: #FF7F00;
        }

        .category-count {
            font-size: 0.8em;
            background: #FF7F00;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 5px;
        }

        /* Coupon Header Styles */
        .coupon-header-container {
            display: flex;
            flex-direction: column;
            margin-left: auto;
            margin-right: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 8px 15px;
            max-width: 350px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .coupon-header-title {
            font-size: 0.9rem;
            font-weight: bold;
            color: #0A0A4A;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }

        .coupon-header-title i {
            color: #FF7F00;
            margin-right: 5px;
        }

        .coupon-header-items {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .coupon-header-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            text-decoration: none;
            padding: 3px 0;
            border-bottom: 1px dashed #eee;
        }

        .coupon-header-item:last-child {
            border-bottom: none;
        }

        .coupon-store {
            font-size: 0.8rem;
            color: #333;
        }

        .coupon-code {
            font-size: 0.8rem;
            font-weight: bold;
            color: #FF7F00;
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px dashed #ccc;
        }

        @media screen and (max-width: 992px) {
            .coupon-header-container {
                max-width: 250px;
                padding: 5px 10px;
            }
        }

        @media screen and (max-width: 768px) {
            .coupon-header-container {
                display: none;
            }
        }
    </style>
</head>
<body>
    <?php
    require_once __DIR__ . '/config.php';
    require_once __DIR__ . '/db_queries.php';

    // Fetch dynamic header HTML from settings table
    function getDynamicHeaderHtml() {
        global $conn;
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'header_html' LIMIT 1");
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row && !empty($row['setting_value']) ? $row['setting_value'] : null;
    }

    // Fetch logo and site name from settings
    function getSetting($key, $default = '') {
        global $conn;
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ? LIMIT 1");
        $stmt->execute([$key]);
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row && $row['setting_value'] !== '' ? $row['setting_value'] : $default;
    }

    $dynamicHeader = getDynamicHeaderHtml();
    if ($dynamicHeader) {
        echo $dynamicHeader;
        return;
    }

    $site_logo = getSetting('site_logo');
    $site_name = getSetting('site_name', 'TRIP NEST');
    $show_coupon_header = getSetting('show_coupon_header', '1');
    $show_search_coupons = getSetting('show_search_coupons', '1');

    // Get current page for active state
    $current_page = basename($_SERVER['PHP_SELF']);
    $current_category = isset($_GET['slug']) ? $_GET['slug'] : '';

    // Get categories with post counts
    try {
        $categories = getCategoriesWithCount();
    } catch(PDOException $e) {
        error_log("Error fetching categories in header: " . $e->getMessage());
        $categories = [];
    }

    // Get active coupons for header if not already set
    if (!isset($active_coupons) || empty($active_coupons)) {
        try {
            $active_coupons = getActiveCoupons(3);
        } catch(PDOException $e) {
            error_log("Error fetching active coupons in header: " . $e->getMessage());
            $active_coupons = [];
        }
    }

    // Fetch dynamic pages for menu (header only or both)
    $menu_pages = [];
    try {
        // Check if location column exists
        $stmt = $conn->query("SHOW COLUMNS FROM pages LIKE 'location'");
        $column_exists = $stmt->fetch();

        if ($column_exists) {
            // If location column exists, filter by location
            $stmt = $conn->query("SELECT title, slug FROM pages WHERE status='published' AND show_in_menu=1 AND (location='header' OR location='both' OR location IS NULL) ORDER BY menu_order ASC, created_at DESC");
        } else {
            // If location column doesn't exist, get all menu pages
            $stmt = $conn->query("SELECT title, slug FROM pages WHERE status='published' AND show_in_menu=1 ORDER BY menu_order ASC, created_at DESC");
        }

        $menu_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error fetching header pages: " . $e->getMessage());
        $menu_pages = [];
    }
    ?>

    <header>
        <div class="header-banner">
            <div class="logo-container">
                <a href="/" style="text-decoration: none;">
                    <?php if ($site_logo): ?>
                        <img src="/uploads/stores/<?php echo htmlspecialchars($site_logo); ?>" alt="<?php echo htmlspecialchars($site_name); ?>" style="height:60px;max-width:180px;object-fit:contain;">
                    <?php else: ?>
                        <div class="logo-text"><?php echo htmlspecialchars($site_name); ?></div>
                    <?php endif; ?>
                </a>
            </div>

            <?php if ($show_coupon_header == '1' && isset($active_coupons) && !empty($active_coupons)): ?>
            <div class="coupon-header-container">
                <div class="coupon-header-title">
                    <i class="fas fa-ticket-alt"></i> Coupons Exclusifs
                </div>
                <div class="coupon-header-items">
                    <?php foreach (array_slice($active_coupons, 0, 3) as $coupon): ?>
                    <a href="/coupon.php?id=<?php echo $coupon['id']; ?>" class="coupon-header-item">
                        <span class="coupon-store"><?php echo htmlspecialchars($coupon['store_name']); ?></span>
                        <span class="coupon-code"><?php echo htmlspecialchars($coupon['code']); ?></span>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <div class="orange-wave"></div>
        </div>

        <nav class="navigation">
            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>

            <div class="nav-links">
                <a href="/" class="nav-item <?php echo ($current_page === 'index.php' && empty($current_category)) ? 'active' : ''; ?>">
                    <i class="fas fa-home"></i>
                    ACCUEIL
                </a>

                <?php foreach ($categories as $category): ?>
                <a href="/category/<?php echo htmlspecialchars($category['slug']); ?>"
                   class="nav-item <?php echo ($current_category === $category['slug']) ? 'active' : ''; ?>">
                    <i class="<?php echo !empty($category['icon']) ? htmlspecialchars($category['icon']) : 'fas fa-folder'; ?>"></i>
                    <?php echo htmlspecialchars(strtoupper($category['name'])); ?>
                    <?php if ($category['post_count'] > 0): ?>
                    <span class="category-count"><?php echo $category['post_count']; ?></span>
                    <?php endif; ?>
                </a>
                <?php endforeach; ?>

                <a href="/coupons" class="nav-item <?php echo ($current_page === 'coupons.php') ? 'active' : ''; ?>">
                    <i class="fas fa-ticket-alt"></i>
                    COUPONS
                </a>

                <?php foreach ($menu_pages as $menu_page): ?>
                    <a href="/page/<?php echo htmlspecialchars($menu_page['slug']); ?>" class="nav-item">
                        <i class="fas fa-file-alt"></i> <?php echo htmlspecialchars($menu_page['title']); ?>
                    </a>
                <?php endforeach; ?>
            </div>

            <?php if ($show_search_coupons == '1'): ?>
            <div class="search-container">
                <form action="/search" method="GET">
                    <input type="text" name="q" class="search-input" placeholder="Rechercher sur le site..." required>
                    <button type="submit" class="search-button">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
            <?php endif; ?>
        </nav>
    </header>

    <script>
        document.querySelector('.mobile-menu-btn').addEventListener('click', function() {
            document.querySelector('.nav-links').classList.toggle('active');
        });
    </script>
</body>
</html>