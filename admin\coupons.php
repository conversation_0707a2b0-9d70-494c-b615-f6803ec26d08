<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/coupon_functions.php';
require_once 'includes/store_functions.php';
require_once 'includes/category_functions.php';

// Check if user is logged in
requireLogin();

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20; // Number of coupons per page

// Get filter parameters
$store_id = isset($_GET['store_id']) ? (int)$_GET['store_id'] : null;
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : null;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Get coupons data
$coupons_data = getAllCoupons($page, $per_page, [
    'store_id' => $store_id,
    'category_id' => $category_id,
    'status' => $status,
    'search' => $search
]);

$coupons = $coupons_data['coupons'];
$total_coupons = $coupons_data['total'];

// Calculate total pages
$total_pages = ceil($total_coupons / $per_page);

// Ensure current page is valid
if ($page < 1) $page = 1;
if ($page > $total_pages) $page = $total_pages;

// Get stores and categories for filters
$stores = getAllStores(1, 1000)['stores'];
$categories = getAllCategories();

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Manage Coupons</h1>
        <a href="add_coupon.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Coupon
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="store_id" class="form-label">Store</label>
                    <select class="form-select" id="store_id" name="store_id">
                        <option value="">All Stores</option>
                        <?php foreach ($stores as $store): ?>
                            <option value="<?php echo $store['id']; ?>" <?php echo $store_id == $store['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($store['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="category_id" class="form-label">Category</label>
                    <select class="form-select" id="category_id" name="category_id">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo $category_id == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        <option value="expired" <?php echo $status === 'expired' ? 'selected' : ''; ?>>Expired</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Search coupons..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Coupons Table -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($coupons)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                    <h5>No coupons found</h5>
                    <p class="text-muted">Try adjusting your filters or add a new coupon.</p>
                    <a href="add_coupon.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Coupon
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Store</th>
                                <th>Category</th>
                                <th>Discount</th>
                                <th>Valid Until</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($coupons as $coupon): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($coupon['logo']): ?>
                                                <img src="<?php echo htmlspecialchars($coupon['logo']); ?>" 
                                                     alt="<?php echo htmlspecialchars($coupon['store_name']); ?>"
                                                     class="me-2" style="width: 24px; height: 24px; object-fit: contain;">
                                            <?php endif; ?>
                                            <strong><?php echo htmlspecialchars($coupon['code']); ?></strong>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($coupon['store_name']); ?></td>
                                    <td><?php echo htmlspecialchars($coupon['category_name']); ?></td>
                                    <td>
                                        <?php if ($coupon['discount_type'] === 'percentage'): ?>
                                            <?php echo $coupon['discount_value']; ?>%
                                        <?php else: ?>
                                            $<?php echo number_format($coupon['discount_value'], 2); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($coupon['end_date']): ?>
                                            <?php echo date('M d, Y', strtotime($coupon['end_date'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">No expiry</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = [
                                            'active' => 'success',
                                            'inactive' => 'secondary',
                                            'expired' => 'danger'
                                        ][$coupon['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo ucfirst($coupon['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($coupon['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="edit_coupon.php?id=<?php echo $coupon['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteCoupon(<?php echo $coupon['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&store_id=<?php echo $store_id; ?>&category_id=<?php echo $category_id; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&store_id=<?php echo $store_id; ?>&category_id=<?php echo $category_id; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&store_id=<?php echo $store_id; ?>&category_id=<?php echo $category_id; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deleteCoupon(couponId) {
    if (confirm('Are you sure you want to delete this coupon? This action cannot be undone.')) {
        fetch('ajax/delete_coupon.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'id=' + couponId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || 'Error deleting coupon');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the coupon');
        });
    }
}
</script>

<?php include 'includes/footer.php'; ?> 