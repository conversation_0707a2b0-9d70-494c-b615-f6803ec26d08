<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate required fields
    if (empty($_POST['id']) || empty($_POST['status'])) {
        throw new Exception('Missing required fields.');
    }

    // Validate status
    $valid_statuses = ['pending', 'approved', 'spam'];
    if (!in_array($_POST['status'], $valid_statuses)) {
        throw new Exception('Invalid status.');
    }

    // Validate comment ID
    $comment_id = (int)$_POST['id'];
    if ($comment_id <= 0) {
        throw new Exception('Invalid comment ID.');
    }

    // Check if comment exists
    $stmt = $conn->prepare("SELECT * FROM comments WHERE id = ?");
    $stmt->execute([$comment_id]);
    if (!$stmt->fetch()) {
        throw new Exception('Comment not found.');
    }

    // Update comment status
    $stmt = $conn->prepare("UPDATE comments SET status = ? WHERE id = ?");
    if ($stmt->execute([$_POST['status'], $comment_id])) {
        $response['success'] = true;
        $response['message'] = 'Comment status updated successfully.';
    } else {
        throw new Exception('Failed to update comment status.');
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 