<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/category_functions.php';

// Log the included files
error_log("Included files loaded");

// Check if user is logged in
if (!isLoggedIn()) {
    error_log("User not logged in");
    header("Location: login.php");
    exit();
}

// Verify database connection and log connection details
try {
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    error_log("PDO Error Mode set to Exception");
    
    // Test database connection and get version
    $version = $conn->query('SELECT VERSION()')->fetchColumn();
    error_log("MySQL Version: " . $version);
    error_log("Database connection successful");
    
    // Log database connection details
    error_log("Database name: " . DB_NAME);
    error_log("Database charset: " . $conn->query('SELECT @@character_set_database')->fetchColumn());
    error_log("Database collation: " . $conn->query('SELECT @@collation_database')->fetchColumn());
} catch(PDOException $e) {
    error_log("Database Connection Error: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get all categories for parent selection
try {
    $categories = getAllCategories();
} catch(Exception $e) {
    $categories = [];
    $error_message = "Error loading categories: " . $e->getMessage();
}

// Include header
include 'includes/header.php';
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Add New Category</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="categories.php">Categories</a></li>
                        <li class="breadcrumb-item active">Add New Category</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Category Details</h3>
                        </div>
                        <form id="addCategoryForm" class="needs-validation" novalidate>
                            <div class="card-body">
                                <div class="form-group mb-3">
                                    <label for="name">Category Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">Please enter a category name.</div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="slug">Slug <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="slug" name="slug" required>
                                    <small class="text-muted">The slug will be automatically generated from the name.</small>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="icon">Category Icon</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="icon" name="icon" value="fas fa-folder" readonly>
                                        <button type="button" class="btn btn-outline-secondary" id="iconPicker">
                                            <i class="fas fa-icons"></i> Select Icon
                                        </button>
                                    </div>
                                    <div class="icon-preview mt-2 text-center">
                                        <i class="fas fa-folder fa-2x"></i>
                                    </div>
                                    <div class="icon-picker-container mt-2 d-none">
                                        <input type="text" class="form-control mb-2" id="iconSearch" placeholder="Search icons...">
                                        <div class="icon-grid"></div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="parent_id">Parent Category</label>
                                    <select class="form-control" id="parent_id" name="parent_id">
                                        <option value="">None (Top Level Category)</option>
                                        <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>">
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="description">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Category
                                </button>
                                <a href="categories.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Tips & Guidelines</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Category Name</h6>
                                <p>Choose a clear, descriptive name that accurately represents the content.</p>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-link"></i> Slug</h6>
                                <p>The slug is automatically generated from the name and used in URLs.</p>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-icons"></i> Icon</h6>
                                <p>Select an icon that visually represents your category. This will be displayed in the navigation.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.icon-picker-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    padding: 15px;
    border-radius: 4px;
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 10px;
}

.icon-option {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.icon-option:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.icon-preview {
    padding: 15px;
    border: 1px dashed #dee2e6;
    border-radius: 4px;
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: none;
}

.was-validated .form-control:invalid ~ .invalid-feedback {
    display: block;
}
</style>

<script>
// Common Font Awesome icons for travel/blog categories
const commonIcons = [
    'fas fa-folder', 'fas fa-suitcase', 'fas fa-plane', 'fas fa-hotel',
    'fas fa-utensils', 'fas fa-camera', 'fas fa-mountain', 'fas fa-umbrella-beach',
    'fas fa-hiking', 'fas fa-map-marked-alt', 'fas fa-passport', 'fas fa-train',
    'fas fa-bus', 'fas fa-car', 'fas fa-bicycle', 'fas fa-ship', 'fas fa-landmark',
    'fas fa-museum', 'fas fa-wine-glass', 'fas fa-ticket-alt', 'fas fa-sun',
    'fas fa-snowflake', 'fas fa-tree', 'fas fa-water', 'fas fa-city'
];

// Icon Picker Functionality
const iconGrid = document.querySelector('.icon-grid');
const iconPreview = document.querySelector('.icon-preview');
const iconInput = document.getElementById('icon');
const iconSearch = document.getElementById('iconSearch');
const iconPickerContainer = document.querySelector('.icon-picker-container');

// Populate icon grid
function populateIcons(icons) {
    iconGrid.innerHTML = '';
    icons.forEach(icon => {
        const div = document.createElement('div');
        div.className = 'icon-option';
        div.innerHTML = `<i class="${icon} fa-lg"></i>`;
        div.onclick = () => selectIcon(icon);
        iconGrid.appendChild(div);
    });
}

// Select icon
function selectIcon(iconClass) {
    iconInput.value = iconClass;
    iconPreview.innerHTML = `<i class="${iconClass} fa-2x"></i>`;
    iconPickerContainer.classList.add('d-none');
}

// Toggle icon picker
document.getElementById('iconPicker').onclick = () => {
    iconPickerContainer.classList.toggle('d-none');
    if (!iconGrid.children.length) {
        populateIcons(commonIcons);
    }
};

// Search icons
iconSearch.addEventListener('input', (e) => {
    const searchTerm = e.target.value.toLowerCase();
    const filteredIcons = commonIcons.filter(icon => 
        icon.toLowerCase().includes(searchTerm)
    );
    populateIcons(filteredIcons);
});

// Auto-generate slug from name
document.getElementById('name').addEventListener('input', function() {
    const slug = this.value
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    document.getElementById('slug').value = slug;
});

// Form submission
document.getElementById('addCategoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (this.checkValidity()) {
        const formData = new FormData(this);
        
        fetch('ajax/add_category.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = 'categories.php';
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while adding the category.');
        });
    }
    
    this.classList.add('was-validated');
});
</script>

<?php include 'includes/footer.php'; ?> 