<?php
require_once 'includes/config.php';
require_once 'includes/db_queries.php';

// Get featured posts for hero section
$featured_posts = getFeaturedPosts(3);
$categories = getAllCategories();
$active_coupons = getActiveCoupons(5);

include 'includes/header.php';
?>

<!-- Link CSS and JS files -->
<link rel="stylesheet" href="assets/css/style.css">
<script src="assets/js/main.js" defer></script>

<div class="container">
    <div class="main-content">
        <div class="content-area">
            <!-- Hero Section with Featured Blog Posts -->
            <section class="hero-section">
                <div class="hero-slideshow">
                    <?php foreach ($featured_posts as $index => $post): ?>
                    <div class="slide <?php echo $index === 0 ? 'active' : ''; ?>">
                        <span class="featured-label">ARTICLE VEDETTE</span>
                        <img src="<?php echo !empty($post['featured_image']) ? 'uploads/posts/' . htmlspecialchars($post['featured_image']) : 'assets/images/default-post.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" 
                             class="hero-image">
                        <div class="hero-content">
                            <div class="hero-content-top">
                                <h2><?php echo htmlspecialchars($post['title']); ?></h2>
                                <div class="hero-meta">
                                    de <a href="#"><?php echo formatDate($post['created_at'], 'l, j F Y'); ?></a> • 
                                    <a href="#"><?php echo $post['views']; ?> vues</a>
                                </div>
                                <p><?php echo getExcerpt($post['excerpt']); ?></p>
                            </div>
                            <div class="hero-content-bottom">
                                <a href="post.php?slug=<?php echo $post['slug']; ?>" class="btn-primary">Lire la suite</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Thumbnail Navigation -->
                <div class="thumbnail-nav">
                    <?php foreach ($featured_posts as $index => $post): ?>
                    <div class="thumbnail-item <?php echo $index === 0 ? 'active' : ''; ?>">
                        <img src="<?php echo !empty($post['featured_image']) ? 'uploads/posts/' . htmlspecialchars($post['featured_image']) : 'assets/images/default-post.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>">
                    </div>
                    <?php endforeach; ?>
                    <div class="next-slide-btn">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </section>
            
            <?php foreach ($categories as $category): 
                $category_posts = getPostsByCategory($category['slug'], 3);
                if (!empty($category_posts)): ?>
            <!-- Category Section -->
            <section class="travel-articles">
                <h2 class="article-heading"><?php echo strtoupper($category['name']); ?></h2>
                
                <div class="articles-grid">
                    <?php foreach ($category_posts as $post): ?>
                    <article class="article">
                        <img src="<?php echo !empty($post['featured_image']) ? 'uploads/posts/' . htmlspecialchars($post['featured_image']) : 'assets/images/default-post.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" 
                             class="article-image">
                        <div class="article-content">
                            <h3><a href="post.php?slug=<?php echo $post['slug']; ?>"><?php echo htmlspecialchars($post['title']); ?></a></h3>
                            <p><?php echo getExcerpt($post['excerpt']); ?></p>
                            <a href="post.php?slug=<?php echo $post['slug']; ?>" class="read-more">Lire la suite</a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; endforeach; ?>
        </div>
        
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
</body>
</html> 