<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/category_functions.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit();
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate category ID
    if (empty($_POST['id'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Category ID is required'
        ]);
        exit();
    }
    
    // Call the updateCategory function
    $result = updateCategory($_POST['id'], $_POST);
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($result);
    exit();
} else {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
    exit();
} 