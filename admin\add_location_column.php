<?php
require_once 'includes/config.php';

// Check if user is logged in as admin
session_start();
if (!isset($_SESSION['admin_id'])) {
    echo "You must be logged in as an admin to run this script.";
    exit;
}

try {
    // Check if the column already exists
    $stmt = $conn->query("SHOW COLUMNS FROM pages LIKE 'location'");
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        // Add the location column to the pages table
        $conn->exec("ALTER TABLE pages ADD COLUMN location ENUM('header', 'footer', 'both') DEFAULT 'header' AFTER show_in_menu");
        echo "Success: The 'location' column has been added to the 'pages' table.";
        
        // Update existing pages to have 'header' as the default location
        $conn->exec("UPDATE pages SET location = 'header' WHERE location IS NULL");
        echo "<br>All existing pages have been set to display in the header by default.";
    } else {
        echo "The 'location' column already exists in the 'pages' table.";
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}

echo "<br><br><a href='pages.php'>Return to Pages</a>";
?>
