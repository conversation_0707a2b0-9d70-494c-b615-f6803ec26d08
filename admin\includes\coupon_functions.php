<?php
/**
 * Get all coupons with pagination and filters
 */
function getAllCoupons($page = 1, $per_page = 20, $filters = []) {
    global $conn;
    
    try {
        $offset = ($page - 1) * $per_page;
        $where_conditions = [];
        $params = [];
        
        // Build WHERE clause based on filters
        if (!empty($filters['store_id'])) {
            $where_conditions[] = "c.store_id = :store_id";
            $params[':store_id'] = $filters['store_id'];
        }
        
        if (!empty($filters['category_id'])) {
            $where_conditions[] = "c.category_id = :category_id";
            $params[':category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['status'])) {
            $where_conditions[] = "c.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $where_conditions[] = "(c.code LIKE :search OR c.description LIKE :search)";
            $params[':search'] = "%{$filters['search']}%";
        }
        
        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        // Get total count
        $count_sql = "SELECT COUNT(*) as total FROM coupons c $where_clause";
        $stmt = $conn->prepare($count_sql);
        $stmt->execute($params);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Get coupons with store and category info
        $sql = "SELECT c.*, s.name as store_name, s.logo, cat.name as category_name 
                FROM coupons c 
                LEFT JOIN stores s ON c.store_id = s.id 
                LEFT JOIN categories cat ON c.category_id = cat.id 
                $where_clause 
                ORDER BY c.created_at DESC 
                LIMIT :offset, :per_page";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->bindValue(':per_page', $per_page, PDO::PARAM_INT);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->execute();
        $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'coupons' => $coupons,
            'total' => $total
        ];
        
    } catch (PDOException $e) {
        error_log("Error in getAllCoupons: " . $e->getMessage());
        return [
            'coupons' => [],
            'total' => 0
        ];
    }
}

/**
 * Get a single coupon by ID
 */
function getCouponById($id) {
    global $conn;
    
    try {
        $sql = "SELECT c.*, s.name as store_name, s.logo, cat.name as category_name 
                FROM coupons c 
                LEFT JOIN stores s ON c.store_id = s.id 
                LEFT JOIN categories cat ON c.category_id = cat.id 
                WHERE c.id = :id";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        error_log("Error in getCouponById: " . $e->getMessage());
        return false;
    }
}

/**
 * Create a new coupon
 */
function createCoupon($data) {
    global $conn;
    
    try {
        $sql = "INSERT INTO coupons (
                    code, description, discount_type, discount_value,
                    store_id, category_id, start_date, end_date,
                    status, affiliate_link, created_at, updated_at
                ) VALUES (
                    :code, :description, :discount_type, :discount_value,
                    :store_id, :category_id, :start_date, :end_date,
                    :status, :affiliate_link, NOW(), NOW()
                )";
        
        $stmt = $conn->prepare($sql);
        return $stmt->execute([
            ':code' => $data['code'],
            ':description' => $data['description'],
            ':discount_type' => $data['discount_type'],
            ':discount_value' => $data['discount_value'],
            ':store_id' => $data['store_id'],
            ':category_id' => $data['category_id'],
            ':start_date' => $data['start_date'],
            ':end_date' => $data['end_date'],
            ':status' => $data['status'],
            ':affiliate_link' => $data['affiliate_link']
        ]);
        
    } catch (PDOException $e) {
        error_log("Error in createCoupon: " . $e->getMessage());
        return false;
    }
}

/**
 * Update an existing coupon
 */
function updateCoupon($data) {
    global $conn;
    
    try {
        $sql = "UPDATE coupons SET 
                    code = :code,
                    description = :description,
                    discount_type = :discount_type,
                    discount_value = :discount_value,
                    store_id = :store_id,
                    category_id = :category_id,
                    start_date = :start_date,
                    end_date = :end_date,
                    status = :status,
                    affiliate_link = :affiliate_link,
                    updated_at = NOW()
                WHERE id = :id";
        
        $stmt = $conn->prepare($sql);
        return $stmt->execute([
            ':id' => $data['id'],
            ':code' => $data['code'],
            ':description' => $data['description'],
            ':discount_type' => $data['discount_type'],
            ':discount_value' => $data['discount_value'],
            ':store_id' => $data['store_id'],
            ':category_id' => $data['category_id'],
            ':start_date' => $data['start_date'],
            ':end_date' => $data['end_date'],
            ':status' => $data['status'],
            ':affiliate_link' => $data['affiliate_link']
        ]);
        
    } catch (PDOException $e) {
        error_log("Error in updateCoupon: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete a coupon
 */
function deleteCoupon($id) {
    global $conn;
    
    try {
        $sql = "DELETE FROM coupons WHERE id = :id";
        $stmt = $conn->prepare($sql);
        return $stmt->execute([':id' => $id]);
        
    } catch (PDOException $e) {
        error_log("Error in deleteCoupon: " . $e->getMessage());
        return false;
    }
}

/**
 * Validate coupon data
 */
function validateCouponData($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['code'])) {
        $errors[] = 'Coupon code is required.';
    }
    
    if (empty($data['store_id'])) {
        $errors[] = 'Store is required.';
    }
    
    if (empty($data['category_id'])) {
        $errors[] = 'Category is required.';
    }
    
    if (empty($data['discount_type'])) {
        $errors[] = 'Discount type is required.';
    }
    
    if (!isset($data['discount_value']) || $data['discount_value'] <= 0) {
        $errors[] = 'Valid discount value is required.';
    }
    
    // Validate discount type and value
    if ($data['discount_type'] === 'percentage' && $data['discount_value'] > 100) {
        $errors[] = 'Percentage discount cannot be greater than 100%.';
    }
    
    // Validate dates
    if (!empty($data['start_date'])) {
        $start_date = strtotime($data['start_date']);
        if ($start_date === false) {
            $errors[] = 'Invalid start date format.';
        }
    }
    
    if (!empty($data['end_date'])) {
        $end_date = strtotime($data['end_date']);
        if ($end_date === false) {
            $errors[] = 'Invalid end date format.';
        }
        
        // Check if end date is after start date
        if (!empty($data['start_date']) && $end_date <= strtotime($data['start_date'])) {
            $errors[] = 'End date must be after start date.';
        }
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * Check if a coupon code already exists
 */
function isCouponCodeExists($code, $exclude_id = null) {
    global $conn;
    
    try {
        $sql = "SELECT COUNT(*) as count FROM coupons WHERE code = :code";
        $params = [':code' => $code];
        
        if ($exclude_id) {
            $sql .= " AND id != :id";
            $params[':id'] = $exclude_id;
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;
        
    } catch (PDOException $e) {
        error_log("Error in isCouponCodeExists: " . $e->getMessage());
        return false;
    }
} 