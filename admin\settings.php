<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Helper to get setting value
$defaults = [
    'site_logo' => '',
    'footer_logo' => '',
    'site_name' => 'TRIP NEST',
    'social_facebook' => '',
    'social_instagram' => '',
    'social_snapchat' => '',
    'social_youtube' => '',
    'social_email' => '',
    'social_tiktok' => '',
    'social_telegram' => '',
    'social_pinterest' => '',
    'footer_copyright' => '© 2013 - 2025 Trip Nest | Offres de vacances & voyages pas chers',
    'footer_credit' => 'Design & Développé par <a href="https://audiencetarget.net/" rel="dofollow" target="_blank">Audience Target</a>',
    'country_link_1' => 'tripnest.de',
    'country_link_1_url' => '#',
    'country_link_2' => 'tripnest.at',
    'country_link_2_url' => '#',
    'show_coupon_header' => '1',
    'show_search_coupons' => '1'
];
function getSetting($key, $default = '') {
    global $conn;
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ? LIMIT 1");
    $stmt->execute([$key]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return $row && $row['setting_value'] !== '' ? $row['setting_value'] : $default;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    foreach (array_keys($defaults) as $key) {
        $value = isset($_POST[$key]) ? trim($_POST[$key]) : '';
        $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        $stmt->execute([$key, $value]);
    }
    // Handle logo uploads
    foreach (['site_logo', 'footer_logo'] as $logoKey) {
        if (!empty($_FILES[$logoKey]['name'])) {
            $target_dir = '../uploads/stores/';
            $ext = strtolower(pathinfo($_FILES[$logoKey]['name'], PATHINFO_EXTENSION));
            $filename = uniqid() . '.' . $ext;
            $target_file = $target_dir . $filename;
            if (move_uploaded_file($_FILES[$logoKey]['tmp_name'], $target_file)) {
                $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
                $stmt->execute([$logoKey, $filename]);
            }
        }
    }
    setMessage('success', 'Settings updated successfully!');
    header('Location: settings.php');
    exit;
}

// Fetch current values
$settings = [];
foreach ($defaults as $key => $def) {
    $settings[$key] = getSetting($key, $def);
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <h1 class="h3 mb-4">Site Settings</h1>
    <?php if ($msg = getMessage()): ?>
        <div class="alert alert-<?php echo $msg['type'] === 'success' ? 'success' : 'danger'; ?>"> <?php echo htmlspecialchars($msg['text']); ?> </div>
    <?php endif; ?>
    <form method="post" enctype="multipart/form-data" class="row g-4">
        <div class="col-md-6">
            <label class="form-label">Site Name</label>
            <input type="text" name="site_name" class="form-control" value="<?php echo htmlspecialchars($settings['site_name']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Site Logo</label><br>
            <?php if ($settings['site_logo']): ?>
                <img src="../uploads/stores/<?php echo htmlspecialchars($settings['site_logo']); ?>" style="height:40px;max-width:120px;object-fit:contain;">
            <?php endif; ?>
            <input type="file" name="site_logo" class="form-control mt-2">
        </div>
        <div class="col-md-6">
            <label class="form-label">Footer Logo</label><br>
            <?php if ($settings['footer_logo']): ?>
                <img src="../uploads/stores/<?php echo htmlspecialchars($settings['footer_logo']); ?>" style="height:40px;max-width:120px;object-fit:contain;">
            <?php endif; ?>
            <input type="file" name="footer_logo" class="form-control mt-2">
        </div>
        <div class="col-md-6">
            <label class="form-label">Facebook</label>
            <input type="text" name="social_facebook" class="form-control" value="<?php echo htmlspecialchars($settings['social_facebook']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Instagram</label>
            <input type="text" name="social_instagram" class="form-control" value="<?php echo htmlspecialchars($settings['social_instagram']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Snapchat</label>
            <input type="text" name="social_snapchat" class="form-control" value="<?php echo htmlspecialchars($settings['social_snapchat']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">YouTube</label>
            <input type="text" name="social_youtube" class="form-control" value="<?php echo htmlspecialchars($settings['social_youtube']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Email</label>
            <input type="text" name="social_email" class="form-control" value="<?php echo htmlspecialchars($settings['social_email']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">TikTok</label>
            <input type="text" name="social_tiktok" class="form-control" value="<?php echo htmlspecialchars($settings['social_tiktok']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Telegram</label>
            <input type="text" name="social_telegram" class="form-control" value="<?php echo htmlspecialchars($settings['social_telegram']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Pinterest</label>
            <input type="text" name="social_pinterest" class="form-control" value="<?php echo htmlspecialchars($settings['social_pinterest']); ?>">
        </div>
        <div class="col-md-12">
            <label class="form-label">Footer Copyright</label>
            <input type="text" name="footer_copyright" class="form-control" value="<?php echo htmlspecialchars($settings['footer_copyright']); ?>">
        </div>
        <div class="col-md-12">
            <label class="form-label">Footer Credit</label>
            <input type="text" name="footer_credit" class="form-control" value="<?php echo htmlspecialchars($settings['footer_credit']); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Country Link 1 Text</label>
            <input type="text" name="country_link_1" class="form-control" value="<?php echo htmlspecialchars(getSetting('country_link_1', 'tripnest.de')); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Country Link 1 URL</label>
            <input type="text" name="country_link_1_url" class="form-control" value="<?php echo htmlspecialchars(getSetting('country_link_1_url', '#')); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Country Link 2 Text</label>
            <input type="text" name="country_link_2" class="form-control" value="<?php echo htmlspecialchars(getSetting('country_link_2', 'tripnest.at')); ?>">
        </div>
        <div class="col-md-6">
            <label class="form-label">Country Link 2 URL</label>
            <input type="text" name="country_link_2_url" class="form-control" value="<?php echo htmlspecialchars(getSetting('country_link_2_url', '#')); ?>">
        </div>

        <div class="col-md-6">
            <label class="form-label">Show Coupon Header</label>
            <select name="show_coupon_header" class="form-control">
                <option value="1" <?php echo getSetting('show_coupon_header', '1') == '1' ? 'selected' : ''; ?>>Yes</option>
                <option value="0" <?php echo getSetting('show_coupon_header', '1') == '0' ? 'selected' : ''; ?>>No</option>
            </select>
        </div>

        <div class="col-md-6">
            <label class="form-label">Show Search Coupons</label>
            <select name="show_search_coupons" class="form-control">
                <option value="1" <?php echo getSetting('show_search_coupons', '1') == '1' ? 'selected' : ''; ?>>Yes</option>
                <option value="0" <?php echo getSetting('show_search_coupons', '1') == '0' ? 'selected' : ''; ?>>No</option>
            </select>
        </div>

        <div class="col-12">
            <button type="submit" class="btn btn-primary">Save Settings</button>
        </div>
    </form>
</div>

<?php include 'includes/footer.php'; ?>