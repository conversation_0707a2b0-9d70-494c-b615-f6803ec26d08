<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/store_functions.php';

// Check if user is logged in
requireLogin();

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Add New Store</h1>
        <a href="stores.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Stores
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Main Store Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="addStoreForm" enctype="multipart/form-data">
                        <div class="mb-4">
                            <label for="name" class="form-label">Store Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-lg" id="name" name="name" required>
                            <div class="form-text">Enter the name of the online store.</div>
                        </div>

                        <div class="mb-4">
                            <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="slug" name="slug" required>
                            <div class="form-text">URL-friendly version of the store name (e.g., amazon, walmart).</div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                            <div class="form-text">Provide a brief description of the store and its offerings.</div>
                        </div>

                        <div class="mb-4">
                            <label for="website_url" class="form-label">Website URL <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="website_url" name="website_url" 
                                   placeholder="https://example.com" required>
                            <div class="form-text">The main website URL of the store.</div>
                        </div>

                        <div class="mb-4">
                            <label for="affiliate_id" class="form-label">Affiliate ID</label>
                            <input type="text" class="form-control" id="affiliate_id" name="affiliate_id">
                            <div class="form-text">Your affiliate ID for this store (if applicable).</div>
                        </div>

                        <div class="mb-4">
                            <label for="logo" class="form-label">Store Logo</label>
                            <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                            <div class="form-text">Recommended size: 200x200 pixels. Maximum file size: 2MB.</div>
                            <div id="logoPreview" class="mt-2"></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Publish</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" form="addStoreForm">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" form="addStoreForm">
                            <i class="fas fa-save"></i> Save Store
                        </button>
                        <a href="stores.php" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from name
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    
    function generateSlug(text) {
        return text
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumeric chars with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/(^-|-$)/g, ''); // Remove leading/trailing hyphens
    }
    
    nameInput.addEventListener('input', function() {
        const name = this.value.trim();
        if (name) {
            const slug = generateSlug(name);
            slugInput.value = slug;
        }
    });

    // Allow manual slug editing
    slugInput.addEventListener('input', function() {
        this.value = generateSlug(this.value);
    });

    // Image preview
    document.getElementById('logo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file size (2MB max)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                this.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    let warning = '';
                    if (this.width < 200 || this.height < 200) {
                        warning = '<div class="alert alert-warning mt-2">Image is smaller than recommended size (200x200)</div>';
                    }
                    document.getElementById('logoPreview').innerHTML = `
                        <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">
                        <div class="text-muted mt-1">Size: ${this.width}x${this.height}</div>
                        ${warning}
                    `;
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // Form submission
    document.getElementById('addStoreForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);

        // Submit the form
        fetch('ajax/add_store.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = 'stores.php';
            } else {
                alert(data.message || 'Error adding store');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while adding the store');
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?> 