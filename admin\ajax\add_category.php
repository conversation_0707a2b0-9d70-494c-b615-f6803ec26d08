<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/category_functions.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit();
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Validate and sanitize inputs
        if (empty($_POST['name'])) {
            throw new Exception('Category name is required.');
        }
        
        if (empty($_POST['slug'])) {
            throw new Exception('Category slug is required.');
        }
        
        $name = sanitize($_POST['name']);
        $slug = sanitize($_POST['slug']);
        $icon = sanitize($_POST['icon'] ?? 'fas fa-folder');
        $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
        $description = sanitize($_POST['description'] ?? '');
        $status = sanitize($_POST['status'] ?? 'active');
        
        // Verify categories table exists and structure
        try {
            // Check if table exists
            $tableExists = $conn->query("SHOW TABLES LIKE 'categories'")->rowCount() > 0;
            if (!$tableExists) {
                // Create categories table if it doesn't exist
                $sql = "CREATE TABLE IF NOT EXISTS categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    slug VARCHAR(255) NOT NULL UNIQUE,
                    icon VARCHAR(50) DEFAULT 'fas fa-folder',
                    description TEXT,
                    parent_id INT,
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                
                $conn->exec($sql);
            } else {
                // Check if icon column exists
                $columnExists = $conn->query("SHOW COLUMNS FROM categories LIKE 'icon'")->rowCount() > 0;
                if (!$columnExists) {
                    $conn->exec("ALTER TABLE categories ADD COLUMN icon VARCHAR(50) DEFAULT 'fas fa-folder' AFTER slug");
                }
            }
        } catch(PDOException $e) {
            throw new Exception('Database structure error: ' . $e->getMessage());
        }
        
        // Check if slug already exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception('A category with this slug already exists.');
        }
        
        // Validate parent category if specified
        if ($parent_id) {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM categories WHERE id = ?");
            $stmt->execute([$parent_id]);
            if ($stmt->fetchColumn() == 0) {
                throw new Exception('Invalid parent category.');
            }
        }
        
        // Insert new category
        $sql = "INSERT INTO categories (name, slug, icon, parent_id, description, status) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt->execute([$name, $slug, $icon, $parent_id, $description, $status])) {
            throw new Exception("Failed to add category.");
        }
        
        $category_id = $conn->lastInsertId();
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Category added successfully.',
            'category_id' => $category_id
        ]);
        
    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
} else {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
} 