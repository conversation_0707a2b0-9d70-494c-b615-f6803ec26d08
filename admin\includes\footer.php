            </div> <!-- End container-fluid -->
        </div> <!-- End content -->
    </div> <!-- End wrapper -->
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js"></script>
    <script src="<?php echo ADMIN_URL; ?>/assets/js/functions.js"></script>
    <script src="<?php echo ADMIN_URL; ?>/assets/js/admin.js"></script>
    
    <script>
        // Initialize TinyMCE
        tinymce.init({
            selector: '.editor',
            height: 400,
            plugins: [
                'advlist autolink lists link image charmap print preview anchor',
                'searchreplace visualblocks code fullscreen',
                'insertdatetime media table paste code help wordcount'
            ],
            toolbar: 'undo redo | formatselect | bold italic backcolor | \
                     alignleft aligncenter alignright alignjustify | \
                     bullist numlist outdent indent | removeformat | help'
        });
        
        // Sidebar toggle
        $(document).ready(function() {
            $('#sidebarCollapse').on('click', function() {
                $('.wrapper').toggleClass('sidebar-collapsed');
            });
            
            // Add delete confirmation to delete buttons
            $('.delete-confirm').on('click', confirmDelete);
        });
    </script>
</body>
</html> 