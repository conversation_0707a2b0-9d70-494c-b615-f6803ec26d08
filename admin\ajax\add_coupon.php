<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/coupon_functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate required fields
    if (empty($_POST['code']) || empty($_POST['store_id']) || 
        empty($_POST['category_id']) || empty($_POST['discount_type']) || !isset($_POST['discount_value'])) {
        throw new Exception('Please fill in all required fields.');
    }

    // Check if coupon code already exists
    if (isCouponCodeExists($_POST['code'])) {
        throw new Exception('A coupon with this code already exists.');
    }

    // Validate discount value
    $discount_value = (float)$_POST['discount_value'];
    if ($discount_value <= 0) {
        throw new Exception('Discount value must be greater than 0.');
    }

    if ($_POST['discount_type'] === 'percentage' && $discount_value > 100) {
        throw new Exception('Percentage discount cannot be greater than 100%.');
    }

    // Validate dates
    if (!empty($_POST['start_date'])) {
        $start_date = strtotime($_POST['start_date']);
        if ($start_date === false) {
            throw new Exception('Invalid start date format.');
        }
    }

    if (!empty($_POST['end_date'])) {
        $end_date = strtotime($_POST['end_date']);
        if ($end_date === false) {
            throw new Exception('Invalid end date format.');
        }

        // Check if end date is after start date
        if (!empty($_POST['start_date']) && $end_date <= strtotime($_POST['start_date'])) {
            throw new Exception('End date must be after start date.');
        }
    }

    // Validate affiliate link if provided
    $affiliate_link = null;
    if (!empty($_POST['affiliate_link'])) {
        if (!filter_var($_POST['affiliate_link'], FILTER_VALIDATE_URL)) {
            throw new Exception('Invalid affiliate link format.');
        }
        $affiliate_link = trim($_POST['affiliate_link']);
    }

    // Prepare coupon data
    $coupon_data = [
        'code' => trim($_POST['code']),
        'description' => trim($_POST['description'] ?? ''),
        'discount_type' => $_POST['discount_type'],
        'discount_value' => $discount_value,
        'store_id' => (int)$_POST['store_id'],
        'category_id' => (int)$_POST['category_id'],
        'start_date' => !empty($_POST['start_date']) ? $_POST['start_date'] : null,
        'end_date' => !empty($_POST['end_date']) ? $_POST['end_date'] : null,
        'status' => $_POST['status'] ?? 'active',
        'affiliate_link' => $affiliate_link
    ];

    // Validate coupon data
    $validation = validateCouponData($coupon_data);
    if (!$validation['valid']) {
        throw new Exception(implode("\n", $validation['errors']));
    }

    // Create the coupon
    if (createCoupon($coupon_data)) {
        $response['success'] = true;
        $response['message'] = 'Coupon added successfully.';
    } else {
        throw new Exception('Failed to add coupon.');
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 