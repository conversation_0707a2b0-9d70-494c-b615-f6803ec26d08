/**
 * TripNest Editor Styles
 * 
 * Custom styles for CKEditor content and travel-specific templates
 */

/* General Editor Styles */
body.cke_editable {
    font-family: 'Open Sans', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    padding: 20px;
    background-color: #fff;
}

/* Headings */
.cke_editable h1, .cke_editable h2, .cke_editable h3,
.cke_editable h4, .cke_editable h5, .cke_editable h6 {
    font-family: 'Montserrat', Arial, sans-serif;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    color: #0A0A4A;
    font-weight: 600;
    line-height: 1.3;
}

.cke_editable h1 { font-size: 2.5em; }
.cke_editable h2 { font-size: 2em; }
.cke_editable h3 { font-size: 1.75em; }
.cke_editable h4 { font-size: 1.5em; }
.cke_editable h5 { font-size: 1.25em; }
.cke_editable h6 { font-size: 1em; }

/* Links */
.cke_editable a {
    color: #0066cc;
    text-decoration: underline;
    transition: color 0.2s ease;
}

.cke_editable a:hover {
    color: #004499;
}

/* Lists */
.cke_editable ul, .cke_editable ol {
    margin: 1em 0;
    padding-left: 2em;
}

.cke_editable li {
    margin-bottom: 0.5em;
}

/* Tables */
.cke_editable table {
    border-collapse: collapse;
    width: 100%;
    margin: 1.5em 0;
}

.cke_editable th, .cke_editable td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: left;
}

.cke_editable th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.cke_editable tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* Blockquotes */
.cke_editable blockquote {
    border-left: 4px solid #0A0A4A;
    padding: 10px 20px;
    margin: 1.5em 0;
    background-color: #f9f9f9;
    font-style: italic;
}

/* Images */
.cke_editable img {
    max-width: 100%;
    height: auto;
    margin: 1em 0;
}

.cke_editable figure {
    margin: 1.5em 0;
}

.cke_editable figcaption {
    font-size: 0.9em;
    color: #666;
    text-align: center;
    padding: 5px 0;
}

/* Code blocks */
.cke_editable pre {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    overflow-x: auto;
    margin: 1.5em 0;
}

.cke_editable code {
    background-color: #f5f5f5;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

/* Travel-specific Template Styles */

/* Destination Overview */
.destination-overview {
    margin: 2em 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 5px solid #0A0A4A;
}

.destination-header {
    color: #0A0A4A;
    border-bottom: 2px solid #0A0A4A;
    padding-bottom: 10px;
    margin-top: 0;
}

/* Travel Tips */
.travel-tip {
    background-color: #e8f4f8;
    border: 1px solid #b8e0ed;
    border-radius: 8px;
    padding: 15px;
    margin: 1.5em 0;
    position: relative;
}

.travel-tip h4 {
    color: #0077b6;
    margin-top: 0;
}

.travel-tip:before {
    content: "💡";
    font-size: 1.5em;
    position: absolute;
    right: 15px;
    top: 10px;
}

/* Itinerary Day */
.itinerary-day {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 1.5em 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.itinerary-day h3 {
    color: #0A0A4A;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-top: 0;
}

/* Price Info */
.price-info {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 10px 15px;
    margin-top: 15px;
    display: inline-block;
}

.price {
    color: #28a745;
    font-weight: bold;
}

/* Location Styling */
.location {
    color: #0A0A4A;
    font-weight: bold;
}

/* Accommodation Review */
.accommodation-review {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 1.5em 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.accommodation-review h3 {
    color: #0A0A4A;
    margin-top: 0;
}

/* Restaurant Review */
.restaurant-review {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 1.5em 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.restaurant-review h3 {
    color: #0A0A4A;
    margin-top: 0;
}

/* Travel Checklist */
.travel-checklist {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 1.5em 0;
}

.travel-checklist h3 {
    color: #0A0A4A;
    margin-top: 0;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.travel-checklist h4 {
    color: #0A0A4A;
    margin-top: 1em;
}

.travel-checklist ul {
    list-style-type: none;
    padding-left: 0;
}

.travel-checklist li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 10px;
}

.travel-checklist li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* Photo Gallery */
.photo-gallery {
    margin: 2em 0;
}

.photo-gallery h3 {
    color: #0A0A4A;
    margin-bottom: 15px;
}

.gallery-description {
    margin-bottom: 20px;
}

.photo-gallery .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.photo-gallery .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 15px;
    margin-bottom: 30px;
}

/* Transportation Guide */
.transportation-guide {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 1.5em 0;
}

.transportation-guide h3 {
    color: #0A0A4A;
    margin-top: 0;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.transport-option {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ddd;
}

.transport-option:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.transport-option h4 {
    color: #0A0A4A;
    margin-top: 0;
}

/* Travel FAQ */
.travel-faq {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 1.5em 0;
}

.travel-faq h3 {
    color: #0A0A4A;
    margin-top: 0;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.faq-item {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ddd;
}

.faq-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.faq-item h4 {
    color: #0A0A4A;
    margin-top: 0;
}

/* Food Guide */
.food-guide {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 1.5em 0;
}

.food-guide h3 {
    color: #0A0A4A;
    margin-top: 0;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.food-item {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ddd;
}

.food-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.food-item h4 {
    color: #0A0A4A;
    margin-top: 0;
}

.food-tip {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 10px 15px;
    margin-top: 15px;
}

/* Budget Breakdown */
.budget-breakdown {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 1.5em 0;
}

.budget-breakdown h3 {
    color: #0A0A4A;
    margin-top: 0;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.expense-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5em 0;
}

.expense-table th, .expense-table td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: left;
}

.expense-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.budget-tip {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 10px 15px;
    margin-top: 15px;
}

/* Highlight */
.highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Important Note */
.important-note {
    color: #dc3545;
    font-weight: bold;
}

/* Travel Quote */
.travel-quote {
    font-style: italic;
    border-left: 4px solid #0A0A4A;
    padding: 15px 20px;
    margin: 1.5em 0;
    background-color: #f9f9f9;
    position: relative;
}

.travel-quote:before {
    content: '"';
    font-size: 3em;
    color: #0A0A4A;
    opacity: 0.2;
    position: absolute;
    left: 10px;
    top: -10px;
}

.travel-quote:after {
    content: '"';
    font-size: 3em;
    color: #0A0A4A;
    opacity: 0.2;
    position: absolute;
    right: 10px;
    bottom: -30px;
}
