// Main JavaScript for Trip Nest

document.addEventListener('DOMContentLoaded', function() {
    // Slideshow functionality
    const slides = document.querySelectorAll('.slide');
    const thumbnails = document.querySelectorAll('.thumbnail-item');
    const nextBtn = document.querySelector('.next-slide-btn');
    let currentSlide = 0;

    function showSlide(index) {
        // Remove active class from all slides and thumbnails
        slides.forEach(slide => slide.classList.remove('active'));
        thumbnails.forEach(thumb => thumb.classList.remove('active'));

        // Add active class to current slide and thumbnail
        slides[index].classList.add('active');
        thumbnails[index].classList.add('active');
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }

    // Add click event to thumbnails
    thumbnails.forEach((thumb, index) => {
        thumb.addEventListener('click', () => {
            currentSlide = index;
            showSlide(currentSlide);
        });
    });

    // Add click event to next button
    nextBtn.addEventListener('click', nextSlide);

    // Auto advance slides every 5 seconds
    setInterval(nextSlide, 5000);

    // Add smooth scrolling to all links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add lazy loading to images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const mobileMenu = document.querySelector('.mobile-menu');

    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('active');
        });
    }
    
    // Hero Slideshow with Thumbnail Navigation
    const heroSlideshow = document.querySelector('.hero-slideshow');
    if (heroSlideshow) {
        const slides = heroSlideshow.querySelectorAll('.slide');
        const thumbnails = document.querySelectorAll('.thumbnail-item');
        const nextBtn = document.querySelector('.next-slide-btn');
        let currentSlide = 0;
        let slideInterval;
        
        function showSlide(n) {
            // Hide all slides
            slides.forEach(slide => {
                slide.classList.remove('active');
            });
            
            // Remove active class from all thumbnails
            thumbnails.forEach(thumb => {
                thumb.classList.remove('active');
            });
            
            // Calculate the correct slide index
            currentSlide = (n + slides.length) % slides.length;
            
            // Show the current slide
            slides[currentSlide].classList.add('active');
            
            // Highlight the current thumbnail
            if (thumbnails.length > 0) {
                thumbnails[currentSlide].classList.add('active');
            }
        }
        
        function nextSlide() {
            showSlide(currentSlide + 1);
        }
        
        // Initialize slideshow
        showSlide(0);
        
        // Start automatic slideshow
        function startSlideshow() {
            slideInterval = setInterval(nextSlide, 7000);
        }
        
        function stopSlideshow() {
            clearInterval(slideInterval);
        }
        
        // Add event listener for next button
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                nextSlide();
                stopSlideshow();
                startSlideshow();
            });
        }
        
        // Add event listeners for thumbnails
        thumbnails.forEach((thumb, index) => {
            thumb.addEventListener('click', () => {
                showSlide(index);
                stopSlideshow();
                startSlideshow();
            });
        });
        
        // Start the slideshow
        startSlideshow();
    }
    
    // Other slideshows (if present)
    const slideshows = document.querySelectorAll('.slideshow:not(.hero-slideshow)');
    if (slideshows.length > 0) {
        slideshows.forEach(slideshow => {
            const slides = slideshow.querySelectorAll('.slide');
            let currentSlide = 0;
            
            function showSlide(n) {
                slides.forEach(slide => slide.style.display = 'none');
                currentSlide = (n + slides.length) % slides.length;
                slides[currentSlide].style.display = 'block';
            }
            
            // Initialize slideshow
            showSlide(0);
            
            // Auto advance slides
            setInterval(() => {
                showSlide(currentSlide + 1);
            }, 5000);
            
            // Previous/Next controls if they exist
            const prevBtn = slideshow.querySelector('.prev');
            const nextBtn = slideshow.querySelector('.next');
            
            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    showSlide(currentSlide - 1);
                });
            }
            
            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    showSlide(currentSlide + 1);
                });
            }
        });
    }
}); 