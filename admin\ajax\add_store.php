<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/store_functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate required fields
    if (empty($_POST['name']) || empty($_POST['slug']) || empty($_POST['website_url'])) {
        throw new Exception('Store name, slug, and website URL are required.');
    }

    // Validate store data
    $validation = validateStoreData($_POST);
    if (!$validation['valid']) {
        throw new Exception(implode("\n", $validation['errors']));
    }

    // Handle file upload if a logo is provided
    $logo_filename = '';
    if (!empty($_FILES['logo']['name'])) {
        // Create uploads directory if it doesn't exist
        $upload_dir = '../../uploads/stores/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Process the uploaded file
        $file_info = pathinfo($_FILES['logo']['name']);
        $extension = strtolower($file_info['extension']);
        
        // Validate file type
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        if (!in_array($extension, $allowed_types)) {
            throw new Exception('Invalid file type. Allowed types: ' . implode(', ', $allowed_types));
        }

        // Validate file size (2MB max)
        if ($_FILES['logo']['size'] > 2 * 1024 * 1024) {
            throw new Exception('File size must be less than 2MB.');
        }

        // Generate unique filename
        $logo_filename = uniqid() . '.' . $extension;
        $upload_path = $upload_dir . $logo_filename;

        // Move uploaded file
        if (!move_uploaded_file($_FILES['logo']['tmp_name'], $upload_path)) {
            throw new Exception('Failed to upload logo.');
        }
    }

    // Prepare store data
    $store_data = [
        'name' => trim($_POST['name']),
        'slug' => trim($_POST['slug']),
        'description' => trim($_POST['description'] ?? ''),
        'logo' => $logo_filename,
        'website_url' => trim($_POST['website_url']),
        'affiliate_id' => trim($_POST['affiliate_id'] ?? ''),
        'status' => $_POST['status'] ?? 'inactive',
        'created_at' => date('Y-m-d H:i:s')
    ];

    // Begin transaction
    $conn->beginTransaction();

    // Insert store
    $sql = "INSERT INTO stores (
                name, slug, description, logo, website_url, 
                affiliate_id, status, created_at
            ) VALUES (
                :name, :slug, :description, :logo, :website_url,
                :affiliate_id, :status, :created_at
            )";

    $stmt = $conn->prepare($sql);
    
    if ($stmt->execute($store_data)) {
        // Commit transaction
        $conn->commit();
        
        $response['success'] = true;
        $response['message'] = 'Store added successfully.';
        $response['store_id'] = $conn->lastInsertId();
    } else {
        throw new Exception('Failed to add store.');
    }

} catch (Exception $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // Delete uploaded logo if exists
    if (!empty($logo_filename)) {
        $logo_path = '../../uploads/stores/' . $logo_filename;
        if (file_exists($logo_path)) {
            unlink($logo_path);
        }
    }
    
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 