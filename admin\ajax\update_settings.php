<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Initialize response array
$response = [
    'success' => false,
    'message' => ''
];

try {
    // Begin transaction
    $conn->beginTransaction();

    // Define valid settings and their validation rules
    $valid_settings = [
        'site_title' => ['required' => true, 'type' => 'string'],
        'site_description' => ['required' => true, 'type' => 'string'],
        'site_email' => ['required' => true, 'type' => 'email'],
        'posts_per_page' => ['required' => true, 'type' => 'number', 'min' => 1, 'max' => 50],
        'comments_per_page' => ['required' => true, 'type' => 'number', 'min' => 1, 'max' => 50],
        'allow_comments' => ['required' => true, 'type' => 'boolean'],
        'require_comment_approval' => ['required' => true, 'type' => 'boolean'],
        'date_format' => ['required' => true, 'type' => 'string', 'allowed' => ['d/m/Y', 'm/d/Y', 'Y-m-d']],
        'time_format' => ['required' => true, 'type' => 'string', 'allowed' => ['H:i', 'h:i A']],
        'currency' => ['required' => true, 'type' => 'string', 'max_length' => 3],
        'currency_symbol' => ['required' => true, 'type' => 'string', 'max_length' => 5],
        'google_analytics' => ['required' => false, 'type' => 'string'],
        'facebook_pixel' => ['required' => false, 'type' => 'string'],
        'recaptcha_site_key' => ['required' => false, 'type' => 'string'],
        'recaptcha_secret_key' => ['required' => false, 'type' => 'string']
    ];

    // Validate and process each setting
    foreach ($valid_settings as $key => $rules) {
        $value = $_POST[$key] ?? null;

        // Check if required
        if ($rules['required'] && empty($value)) {
            throw new Exception("Setting '$key' is required.");
        }

        // Skip validation if not required and empty
        if (!$rules['required'] && empty($value)) {
            continue;
        }

        // Validate based on type
        switch ($rules['type']) {
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception("Invalid email format for '$key'.");
                }
                break;

            case 'number':
                $value = (int)$value;
                if ($value < ($rules['min'] ?? 0) || $value > ($rules['max'] ?? PHP_INT_MAX)) {
                    throw new Exception("Value for '$key' must be between {$rules['min']} and {$rules['max']}.");
                }
                break;

            case 'boolean':
                $value = $value ? '1' : '0';
                break;

            case 'string':
                if (isset($rules['allowed']) && !in_array($value, $rules['allowed'])) {
                    throw new Exception("Invalid value for '$key'.");
                }
                if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
                    throw new Exception("Value for '$key' exceeds maximum length of {$rules['max_length']} characters.");
                }
                break;
        }

        // Update or insert setting
        $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) 
                               VALUES (:key, :value) 
                               ON DUPLICATE KEY UPDATE setting_value = :value");
        
        if (!$stmt->execute([':key' => $key, ':value' => $value])) {
            throw new Exception("Failed to update setting '$key'.");
        }
    }

    // Commit transaction
    $conn->commit();
    
    $response['success'] = true;
    $response['message'] = 'Settings updated successfully.';

} catch (Exception $e) {
    // Rollback transaction if active
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    $response['message'] = $e->getMessage();
}

// Send JSON response
header('Content-Type: application/json');
echo json_encode($response); 